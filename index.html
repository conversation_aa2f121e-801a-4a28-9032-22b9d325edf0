<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة مخزون مطحنة</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="alternate icon" href="favicon.svg">
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- Navigation Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo-container">
                    <img src="logo.svg" alt="شعار مطحنة" class="company-logo">
                </div>
            </div>
            <ul class="nav-menu">
                <li class="nav-item active" data-page="user-info">
                    <i class="fas fa-user"></i>
                    <span>بيانات المستخدم</span>
                </li>
                <li class="nav-item" data-page="raw-materials">
                    <i class="fas fa-boxes"></i>
                    <span>المواد الخام</span>
                </li>
                <li class="nav-item" data-page="finished-products">
                    <i class="fas fa-box-open"></i>
                    <span>المواد الجاهزة</span>
                </li>
                <li class="nav-item" data-page="mixtures">
                    <i class="fas fa-blender"></i>
                    <span>الخلطات - المطاحين</span>
                </li>
                <li class="nav-item" data-page="mills">
                    <i class="fas fa-cogs"></i>
                    <span>المطاحين</span>
                </li>
                <li class="nav-item" data-page="recipes-production">
                    <i class="fas fa-flask"></i>
                    <span>الوصفات والإنتاج</span>
                </li>
                <li class="nav-item" data-page="production-log">
                    <i class="fas fa-clipboard-list"></i>
                    <span>سجل الإنتاج</span>
                </li>
                <li class="nav-item" data-page="reports">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير</span>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- User Info Page -->
            <div id="user-info" class="page active">
                <div class="page-header">
                    <div class="page-title-section">
                        <div class="page-logo">
                            <img src="logo.svg" alt="شعار مطحنة" class="page-logo-img">
                        </div>
                        <h1><i class="fas fa-user"></i> بيانات المستخدم</h1>
                    </div>
                    <div class="header-actions">
                        <button class="btn btn-secondary fullscreen-toggle" onclick="toggleFullscreen()">
                            <i class="fas fa-expand"></i> ملء الشاشة
                        </button>
                    </div>
                    <div class="header-info">
                        <span class="info-badge">
                            <i class="fas fa-calendar-alt"></i>
                            النظام يستخدم التقويم الميلادي (الجريجوري)
                        </span>
                    </div>
                </div>
                <div class="content-card">
                    <form class="user-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="userName">اسم المستخدم</label>
                                <input type="text" id="userName" name="userName" placeholder="أدخل اسم المستخدم">
                            </div>
                            <div class="form-group">
                                <label for="userEmail">البريد الإلكتروني</label>
                                <input type="email" id="userEmail" name="userEmail" placeholder="أدخل البريد الإلكتروني">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="userPhone">رقم الهاتف</label>
                                <input type="tel" id="userPhone" name="userPhone" placeholder="أدخل رقم الهاتف">
                            </div>
                            <div class="form-group">
                                <label for="userRole">الدور الوظيفي</label>
                                <select id="userRole" name="userRole">
                                    <option value="">اختر الدور</option>
                                    <option value="admin">مدير النظام</option>
                                    <option value="manager">مدير المطحنة</option>
                                    <option value="operator">مشغل</option>
                                    <option value="viewer">مراقب</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label for="userAddress">العنوان</label>
                                <textarea id="userAddress" name="userAddress" placeholder="أدخل العنوان الكامل" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ البيانات
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo"></i> إعادة تعيين
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Raw Materials Page -->
            <div id="raw-materials" class="page">
                <div class="page-header">
                    <h1><i class="fas fa-boxes"></i> المواد الخام</h1>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="openAddModal('raw-material')">
                            <i class="fas fa-plus"></i> إضافة مادة خام
                        </button>
                        <button class="btn btn-success" onclick="exportToPDF('raw-materials')">
                            <i class="fas fa-file-pdf"></i> تصدير PDF
                        </button>
                        <button class="btn btn-info" onclick="printTable('raw-materials')">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button class="btn btn-secondary fullscreen-toggle" onclick="toggleFullscreen()">
                            <i class="fas fa-expand"></i> ملء الشاشة
                        </button>
                    </div>
                </div>
                <div class="content-card">
                    <div class="search-bar">
                        <input type="text" id="rawMaterialSearch" placeholder="البحث في المواد الخام..." onkeyup="searchTable('rawMaterialsTable', this.value)">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="table-container">
                        <table id="rawMaterialsTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم الكود</th>
                                    <th>اسم المادة</th>
                                    <th>الوزن (كجم)</th>
                                    <th>الكمية</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="rawMaterialsBody">
                                <!-- Data will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Finished Products Page -->
            <div id="finished-products" class="page">
                <div class="page-header">
                    <h1><i class="fas fa-box-open"></i> المواد الجاهزة</h1>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="openAddModal('finished-product')">
                            <i class="fas fa-plus"></i> إضافة مادة جاهزة
                        </button>
                        <button class="btn btn-success" onclick="exportToPDF('finished-products')">
                            <i class="fas fa-file-pdf"></i> تصدير PDF
                        </button>
                        <button class="btn btn-info" onclick="printTable('finished-products')">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button class="btn btn-secondary fullscreen-toggle" onclick="toggleFullscreen()">
                            <i class="fas fa-expand"></i> ملء الشاشة
                        </button>
                    </div>
                </div>
                <div class="content-card">
                    <div class="search-bar">
                        <input type="text" id="finishedProductSearch" placeholder="البحث في المواد الجاهزة..." onkeyup="searchTable('finishedProductsTable', this.value)">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="table-container">
                        <table id="finishedProductsTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم الكود</th>
                                    <th>اسم المنتج</th>
                                    <th>الوزن (كجم)</th>
                                    <th>الكمية المتاحة</th>
                                    <th>تاريخ الإنتاج</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="finishedProductsBody">
                                <!-- Data will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Mixtures Page -->
            <div id="mixtures" class="page">
                <div class="page-header">
                    <h1><i class="fas fa-blender"></i> الخلطات - المطاحين</h1>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="openAddModal('mixture')">
                            <i class="fas fa-plus"></i> إضافة خلطة جديدة
                        </button>
                        <button class="btn btn-success" onclick="exportToPDF('mixtures')">
                            <i class="fas fa-file-pdf"></i> تصدير PDF
                        </button>
                        <button class="btn btn-info" onclick="printTable('mixtures')">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button class="btn btn-secondary fullscreen-toggle" onclick="toggleFullscreen()">
                            <i class="fas fa-expand"></i> ملء الشاشة
                        </button>
                    </div>
                </div>
                <div class="content-card">
                    <div class="search-bar">
                        <input type="text" id="mixtureSearch" placeholder="البحث في الخلطات..." onkeyup="searchTable('mixturesTable', this.value)">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="table-container">
                        <table id="mixturesTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>كود التشغيل</th>
                                    <th>اسم الخلطة</th>
                                    <th>المكونات</th>
                                    <th>الوزن الإجمالي</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="mixturesBody">
                                <!-- Data will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Mills Page -->
            <div id="mills" class="page">
                <div class="page-header">
                    <h1><i class="fas fa-cogs"></i> المطاحين</h1>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="openAddModal('mill')">
                            <i class="fas fa-plus"></i> إضافة مطحنة جديدة
                        </button>
                        <button class="btn btn-success" onclick="exportToPDF('mills')">
                            <i class="fas fa-file-pdf"></i> تصدير PDF
                        </button>
                        <button class="btn btn-info" onclick="printTable('mills')">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button class="btn btn-secondary fullscreen-toggle" onclick="toggleFullscreen()">
                            <i class="fas fa-expand"></i> ملء الشاشة
                        </button>
                    </div>
                </div>
                <div class="content-card">
                    <div class="search-bar">
                        <input type="text" id="millSearch" placeholder="البحث في المطاحين..." onkeyup="searchTable('millsTable', this.value)">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="table-container">
                        <table id="millsTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم المطحنة</th>
                                    <th>اسم المطحنة</th>
                                    <th>النوع</th>
                                    <th>السعة (كجم/ساعة)</th>
                                    <th>نسبة التشغيل (%)</th>
                                    <th>ساعات التشغيل اليومية</th>
                                    <th>الإنتاج اليومي (كجم)</th>
                                    <th>تكلفة التشغيل (ريال/ساعة)</th>
                                    <th>حالة التشغيل</th>
                                    <th>آخر صيانة</th>
                                    <th>الصيانة القادمة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="millsBody">
                                <!-- Data will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Mill Performance Chart -->
                <div class="content-card">
                    <h3><i class="fas fa-chart-line"></i> أداء المطاحين</h3>
                    <div class="mills-performance">
                        <div class="performance-grid" id="performanceGrid">
                            <!-- Performance cards will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Production Statistics -->
                <div class="content-card">
                    <h3><i class="fas fa-chart-pie"></i> إحصائيات الإنتاج</h3>
                    <div class="production-stats">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-play-circle"></i>
                                </div>
                                <div class="stat-info">
                                    <h4 id="activeMills">0</h4>
                                    <p>مطاحين نشطة</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-pause-circle"></i>
                                </div>
                                <div class="stat-info">
                                    <h4 id="idleMills">0</h4>
                                    <p>مطاحين متوقفة</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-tools"></i>
                                </div>
                                <div class="stat-info">
                                    <h4 id="maintenanceMills">0</h4>
                                    <p>تحت الصيانة</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="stat-info">
                                    <h4 id="avgEfficiency">0%</h4>
                                    <p>متوسط الكفاءة</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-info">
                                    <h4 id="totalWorkingHours">0</h4>
                                    <p>إجمالي ساعات التشغيل</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-weight-hanging"></i>
                                </div>
                                <div class="stat-info">
                                    <h4 id="totalProduction">0</h4>
                                    <p>الإنتاج اليومي (كجم)</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="stat-info">
                                    <h4 id="totalCost">0</h4>
                                    <p>التكلفة اليومية (ريال)</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="stat-info">
                                    <h4 id="upcomingMaintenance">0</h4>
                                    <p>صيانات قادمة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Productivity Analysis -->
                <div class="content-card">
                    <h3><i class="fas fa-analytics"></i> تحليل الإنتاجية</h3>
                    <div class="productivity-analysis">
                        <div class="analysis-grid">
                            <div class="analysis-card">
                                <h4>كفاءة الإنتاج</h4>
                                <div class="efficiency-comparison">
                                    <div class="comparison-item">
                                        <span class="label">الإنتاج الفعلي:</span>
                                        <span class="value" id="actualProduction">0 كجم</span>
                                    </div>
                                    <div class="comparison-item">
                                        <span class="label">الإنتاج المتوقع:</span>
                                        <span class="value" id="expectedProduction">0 كجم</span>
                                    </div>
                                    <div class="comparison-item">
                                        <span class="label">نسبة الإنجاز:</span>
                                        <span class="value achievement" id="achievementRate">0%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="analysis-card">
                                <h4>تحليل التكاليف</h4>
                                <div class="cost-breakdown">
                                    <div class="cost-item">
                                        <span class="label">تكلفة الكهرباء:</span>
                                        <span class="value" id="electricityCost">0 ريال</span>
                                    </div>
                                    <div class="cost-item">
                                        <span class="label">تكلفة الصيانة:</span>
                                        <span class="value" id="maintenanceCost">0 ريال</span>
                                    </div>
                                    <div class="cost-item">
                                        <span class="label">تكلفة العمالة:</span>
                                        <span class="value" id="laborCost">0 ريال</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Maintenance Schedule -->
                <div class="content-card">
                    <h3><i class="fas fa-calendar-check"></i> جدولة الصيانة</h3>
                    <div class="maintenance-schedule">
                        <div class="schedule-table-container">
                            <table class="schedule-table">
                                <thead>
                                    <tr>
                                        <th>المطحنة</th>
                                        <th>نوع الصيانة</th>
                                        <th>آخر صيانة</th>
                                        <th>الصيانة القادمة</th>
                                        <th>الأولوية</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="maintenanceScheduleBody">
                                    <!-- Maintenance schedule will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recipes and Production Planning Page -->
            <div id="recipes-production" class="page">
                <div class="page-header">
                    <h1><i class="fas fa-flask"></i> إدارة الوصفات وتخطيط الإنتاج</h1>
                    <div class="header-actions">
                        <button class="btn btn-secondary fullscreen-toggle" onclick="toggleFullscreen()">
                            <i class="fas fa-expand"></i> ملء الشاشة
                        </button>
                    </div>
                    <div class="header-info">
                        <span class="info-badge">
                            <i class="fas fa-info-circle"></i>
                            إدارة شاملة للوصفات والخلطات مع تخطيط الإنتاج
                        </span>
                    </div>
                </div>

                <!-- Recipe Management Section -->
                <div class="content-card">
                    <h3><i class="fas fa-flask"></i> إدارة الوصفات والخلطات</h3>
                    <div class="recipe-management">
                        <div class="recipe-header">
                            <button class="btn btn-primary" onclick="openRecipeModal()">
                                <i class="fas fa-plus"></i> إنشاء وصفة جديدة
                            </button>
                            <button class="btn btn-success" onclick="exportRecipes()">
                                <i class="fas fa-file-pdf"></i> تصدير الوصفات
                            </button>
                            <button class="btn btn-info" onclick="printRecipes()">
                                <i class="fas fa-print"></i> طباعة الوصفات
                            </button>
                        </div>

                        <div class="recipes-grid" id="recipesGrid">
                            <!-- Recipe cards will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Production Planning Section -->
                <div class="content-card">
                    <h3><i class="fas fa-calendar-alt"></i> تخطيط الإنتاج</h3>
                    <div class="production-planning">
                        <div class="planning-header">
                            <button class="btn btn-primary" onclick="openProductionModal()">
                                <i class="fas fa-plus"></i> إضافة مهمة إنتاج
                            </button>
                            <button class="btn btn-warning" onclick="loadDrafts()">
                                <i class="fas fa-drafting-compass"></i> المسودات
                            </button>
                            <button class="btn btn-info" onclick="showProductionHistory()">
                                <i class="fas fa-history"></i> سجل الإنتاج
                            </button>
                        </div>

                        <!-- Production Queue -->
                        <div class="production-queue-section">
                            <h4><i class="fas fa-list-ol"></i> قائمة الإنتاج الحالية</h4>
                            <div class="table-container">
                                <table id="productionQueueTable" class="data-table">
                                    <thead>
                                        <tr>
                                            <th>الوصفة</th>
                                            <th>المطحنة</th>
                                            <th>الكمية المطلوبة</th>
                                            <th>الكمية المنتجة</th>
                                            <th>التقدم</th>
                                            <th>الوقت المتوقع</th>
                                            <th>التكلفة المقدرة</th>
                                            <th>تاريخ البدء</th>
                                            <th>الأولوية</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="productionQueueBody">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Production Statistics -->
                        <div class="production-stats-section">
                            <h4><i class="fas fa-chart-bar"></i> إحصائيات الإنتاج</h4>
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-tasks"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h3 id="totalJobs">0</h3>
                                        <p>إجمالي المهام</p>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-play"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h3 id="runningJobs">0</h3>
                                        <p>قيد التشغيل</p>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h3 id="completedJobs">0</h3>
                                        <p>مكتملة</p>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h3 id="pendingJobs">0</h3>
                                        <p>في الانتظار</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Production Log Page -->
            <div id="production-log" class="page">
                <div class="page-header">
                    <h1><i class="fas fa-clipboard-list"></i> سجل الإنتاج</h1>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="exportProductionLog()">
                            <i class="fas fa-file-pdf"></i> تصدير السجل
                        </button>
                        <button class="btn btn-info" onclick="printProductionLog()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button class="btn btn-success" onclick="refreshProductionLog()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                        <button class="btn btn-secondary fullscreen-toggle" onclick="toggleFullscreen()">
                            <i class="fas fa-expand"></i> ملء الشاشة
                        </button>
                    </div>
                </div>

                <!-- Production Summary Cards -->
                <div class="production-summary-cards">
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="summary-info">
                            <h3 id="totalProductionJobs">0</h3>
                            <p>إجمالي مهام الإنتاج</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="summary-info">
                            <h3 id="completedJobs">0</h3>
                            <p>مهام مكتملة</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-weight-hanging"></i>
                        </div>
                        <div class="summary-info">
                            <h3 id="totalProducedWeight">0</h3>
                            <p>إجمالي الإنتاج (كجم)</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="summary-info">
                            <h3 id="totalProductionCost">0</h3>
                            <p>إجمالي التكلفة (ريال)</p>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="content-card">
                    <h3><i class="fas fa-filter"></i> البحث والفلترة</h3>
                    <div class="filters-section">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="filterRecipe">فلترة حسب الوصفة</label>
                                <select id="filterRecipe" onchange="filterProductionLog()">
                                    <option value="">جميع الوصفات</option>
                                    <!-- Options will be populated by JavaScript -->
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="filterMill">فلترة حسب المطحنة</label>
                                <select id="filterMill" onchange="filterProductionLog()">
                                    <option value="">جميع المطاحين</option>
                                    <!-- Options will be populated by JavaScript -->
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="filterStatus">فلترة حسب الحالة</label>
                                <select id="filterStatus" onchange="filterProductionLog()">
                                    <option value="">جميع الحالات</option>
                                    <option value="completed">مكتمل</option>
                                    <option value="running">قيد التشغيل</option>
                                    <option value="paused">متوقف مؤقتاً</option>
                                    <option value="pending">في الانتظار</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="filterDateFrom">من تاريخ</label>
                                <input type="date" id="filterDateFrom" onchange="filterProductionLog()">
                            </div>
                            <div class="form-group">
                                <label for="filterDateTo">إلى تاريخ</label>
                                <input type="date" id="filterDateTo" onchange="filterProductionLog()">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <button class="btn btn-secondary" onclick="clearFilters()">
                                    <i class="fas fa-times"></i> مسح الفلاتر
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Production Log Table -->
                <div class="content-card">
                    <h3><i class="fas fa-list"></i> سجل الإنتاج التفصيلي</h3>
                    <div class="table-container">
                        <table id="productionLogTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم المهمة</th>
                                    <th>الوصفة</th>
                                    <th>المطحنة</th>
                                    <th>الكمية المطلوبة</th>
                                    <th>الكمية المنتجة</th>
                                    <th>نسبة الإنجاز</th>
                                    <th>تاريخ البدء</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="productionLogBody">
                                <!-- Data will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Product-wise Production Details -->
                <div class="content-card">
                    <h3><i class="fas fa-chart-pie"></i> تفاصيل الإنتاج حسب المنتج</h3>
                    <div id="productWiseDetails" class="product-wise-container">
                        <!-- Product details will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Reports Page -->
            <div id="reports" class="page">
                <div class="page-header">
                    <h1><i class="fas fa-chart-bar"></i> التقارير</h1>
                    <div class="header-actions">
                        <button class="btn btn-secondary fullscreen-toggle" onclick="toggleFullscreen()">
                            <i class="fas fa-expand"></i> ملء الشاشة
                        </button>
                    </div>
                </div>
                <div class="reports-grid">
                    <div class="report-card" onclick="generateReport('raw-materials')">
                        <i class="fas fa-boxes"></i>
                        <h3>تقرير المواد الخام</h3>
                        <p>عرض تفصيلي لجميع المواد الخام والكميات</p>
                    </div>
                    <div class="report-card" onclick="generateReport('finished-products')">
                        <i class="fas fa-box-open"></i>
                        <h3>تقرير المواد الجاهزة</h3>
                        <p>عرض تفصيلي للمنتجات الجاهزة</p>
                    </div>
                    <div class="report-card" onclick="generateReport('mixtures')">
                        <i class="fas fa-blender"></i>
                        <h3>تقرير الخلطات</h3>
                        <p>عرض تفصيلي لجميع الخلطات والمكونات</p>
                    </div>
                    <div class="report-card" onclick="generateReport('inventory-summary')">
                        <i class="fas fa-chart-pie"></i>
                        <h3>ملخص المخزون</h3>
                        <p>تقرير شامل لحالة المخزون</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">إضافة عنصر جديد</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addForm">
                    <div id="formFields">
                        <!-- Form fields will be populated by JavaScript -->
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal()">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="editModalTitle">تعديل العنصر</h2>
                <span class="close" onclick="closeEditModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <div id="editFormFields">
                        <!-- Form fields will be populated by JavaScript -->
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ التعديل
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeEditModal()">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Recipe Modal -->
    <div id="recipeModal" class="modal">
        <div class="modal-content recipe-modal-content">
            <div class="modal-header">
                <h2>إنشاء وصفة جديدة</h2>
                <span class="close" onclick="closeRecipeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="recipeForm">
                    <div class="recipe-basic-info">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="recipeName">اسم الوصفة</label>
                                <input type="text" id="recipeName" name="recipeName" required>
                            </div>
                            <div class="form-group">
                                <label for="recipeCode">كود الوصفة</label>
                                <input type="text" id="recipeCode" name="recipeCode" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="recipeDescription">وصف الوصفة</label>
                                <textarea id="recipeDescription" name="recipeDescription" rows="2"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="totalWeight">الوزن الإجمالي (كجم)</label>
                                <input type="number" id="totalWeight" name="totalWeight" min="0" step="0.1" required>
                            </div>
                        </div>
                    </div>

                    <div class="recipe-ingredients">
                        <h4>المكونات والنسب</h4>
                        <div class="ingredients-header">
                            <button type="button" class="btn btn-secondary" onclick="addIngredient()">
                                <i class="fas fa-plus"></i> إضافة مكون
                            </button>
                            <span class="total-percentage">النسبة الإجمالية: <span id="totalPercentage">0%</span></span>
                        </div>
                        <div id="ingredientsList" class="ingredients-list">
                            <!-- Ingredients will be added dynamically -->
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ الوصفة
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeRecipeModal()">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Production Modal -->
    <div id="productionModal" class="modal">
        <div class="modal-content production-modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-cogs"></i> بدء إنتاج جديد</h2>
                <span class="close" onclick="closeProductionModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="productionForm">
                    <!-- Step 1: Recipe Selection -->
                    <div class="production-step">
                        <h3><i class="fas fa-flask"></i> الخطوة الأولى: اختيار الوصفة</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="selectedRecipe">الوصفة المطلوبة</label>
                                <select id="selectedRecipe" name="selectedRecipe" required onchange="updateRecipeDetails()">
                                    <option value="">اختر الوصفة المطلوب إنتاجها</option>
                                    <!-- Options will be populated by JavaScript -->
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="productionQuantity">الكمية المطلوبة (كجم)</label>
                                <input type="number" id="productionQuantity" name="productionQuantity" min="1" step="0.1" required onchange="updateProductionCalculations()">
                            </div>
                        </div>

                        <div id="recipeDetails" class="recipe-details-expanded">
                            <!-- Recipe details will be shown here -->
                        </div>
                    </div>

                    <!-- Step 2: Mill Selection -->
                    <div class="production-step">
                        <h3><i class="fas fa-cogs"></i> الخطوة الثانية: اختيار المطحنة</h3>
                        <div class="mills-selection">
                            <div id="millsGrid" class="mills-grid">
                                <!-- Mill cards will be populated by JavaScript -->
                            </div>
                            <input type="hidden" id="selectedMill" name="selectedMill" required>
                        </div>
                    </div>

                    <!-- Step 3: Production Planning -->
                    <div class="production-step">
                        <h3><i class="fas fa-calendar-alt"></i> الخطوة الثالثة: تخطيط الإنتاج</h3>
                        <div class="production-planning-details">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="startDate">تاريخ البدء</label>
                                    <input type="date" id="startDate" name="startDate" required>
                                </div>
                                <div class="form-group">
                                    <label for="priority">الأولوية</label>
                                    <select id="priority" name="priority" required>
                                        <option value="">اختر الأولوية</option>
                                        <option value="عالية">عالية</option>
                                        <option value="متوسطة">متوسطة</option>
                                        <option value="منخفضة">منخفضة</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="estimatedTime">الوقت المتوقع (ساعات)</label>
                                    <input type="number" id="estimatedTime" name="estimatedTime" min="0.1" step="0.1" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="estimatedCost">التكلفة المتوقعة (ريال)</label>
                                    <input type="number" id="estimatedCost" name="estimatedCost" min="0" step="0.01" readonly>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group full-width">
                                    <label for="productionNotes">ملاحظات الإنتاج</label>
                                    <textarea id="productionNotes" name="productionNotes" rows="3" placeholder="أدخل أي ملاحظات خاصة بهذا الإنتاج..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 4: Material Requirements -->
                    <div class="production-step">
                        <h3><i class="fas fa-boxes"></i> الخطوة الرابعة: متطلبات المواد</h3>
                        <div id="materialRequirements" class="material-requirements">
                            <!-- Material requirements will be calculated and shown here -->
                        </div>
                    </div>

                    <!-- Production Summary -->
                    <div class="production-summary">
                        <h3><i class="fas fa-clipboard-check"></i> ملخص الإنتاج</h3>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <span class="label">الوصفة:</span>
                                <span class="value" id="summaryRecipe">-</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">المطحنة:</span>
                                <span class="value" id="summaryMill">-</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">الكمية:</span>
                                <span class="value" id="summaryQuantity">-</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">الوقت المتوقع:</span>
                                <span class="value" id="summaryTime">-</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">التكلفة المتوقعة:</span>
                                <span class="value" id="summaryCost">-</span>
                            </div>
                            <div class="summary-item">
                                <span class="label">الأولوية:</span>
                                <span class="value" id="summaryPriority">-</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary btn-large">
                            <i class="fas fa-play"></i> بدء الإنتاج
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeProductionModal()">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                        <button type="button" class="btn btn-info" onclick="saveAsDraft()">
                            <i class="fas fa-save"></i> حفظ كمسودة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Production Details Modal -->
    <div id="productionDetailsModal" class="modal">
        <div class="modal-content production-details-modal">
            <div class="modal-header">
                <h2 id="productionDetailsTitle">تفاصيل مهمة الإنتاج</h2>
                <span class="close" onclick="closeProductionDetailsModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="productionDetailsContent">
                    <!-- Production details will be populated by JavaScript -->
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-primary" onclick="exportJobDetails()">
                        <i class="fas fa-file-pdf"></i> تصدير التفاصيل
                    </button>
                    <button type="button" class="btn btn-info" onclick="printJobDetails()">
                        <i class="fas fa-print"></i> طباعة التفاصيل
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeProductionDetailsModal()">
                        <i class="fas fa-times"></i> إغلاق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
