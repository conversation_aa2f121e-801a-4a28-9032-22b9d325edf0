/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
    color: #333;
}

/* App Container */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Navigation */
.sidebar {
    width: 280px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
}

.sidebar-header {
    padding: 1.5rem 1rem;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.company-logo {
    width: 100%;
    max-width: 200px;
    height: auto;
    filter: drop-shadow(0 2px 8px rgba(0,0,0,0.3));
    transition: all 0.3s ease;
}

.company-logo:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 4px 12px rgba(0,0,0,0.4));
    animation: logoGlow 2s ease-in-out infinite alternate;
}

@keyframes logoGlow {
    0% {
        filter: drop-shadow(0 2px 8px rgba(0,0,0,0.3));
    }
    100% {
        filter: drop-shadow(0 4px 16px rgba(102, 126, 234, 0.6));
    }
}

@keyframes logoFloat {
    0%, 100% {
        transform: scale(1) translateY(0px);
    }
    50% {
        transform: scale(1.02) translateY(-2px);
    }
}

.company-logo:hover {
    animation: logoFloat 2s ease-in-out infinite;
}

.sidebar-header i {
    font-size: 2.5rem;
    color: #27ae60;
    margin-bottom: 0.5rem;
}

.sidebar-header h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #ecf0f1;
}

.nav-menu {
    list-style: none;
    padding: 1rem 0;
}

.nav-item {
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-item:hover {
    background: rgba(255,255,255,0.1);
    border-right-color: #27ae60;
}

.nav-item.active {
    background: rgba(39, 174, 96, 0.2);
    border-right-color: #27ae60;
}

.nav-item i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.nav-item span {
    font-weight: 500;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-right: 280px;
    padding: 2rem;
    background: rgba(255,255,255,0.95);
    min-height: 100vh;
}

/* Page Styles */
.page {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e74c3c;
}

.page-title-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-logo {
    display: flex;
    align-items: center;
}

.page-logo-img {
    width: 80px;
    height: auto;
    filter: drop-shadow(0 2px 6px rgba(0,0,0,0.2));
    transition: all 0.3s ease;
}

.page-logo-img:hover {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 3px 12px rgba(102, 126, 234, 0.4));
    animation: logoSpin 1s ease-in-out;
}

@keyframes logoSpin {
    0% {
        transform: scale(1) rotate(0deg);
    }
    50% {
        transform: scale(1.1) rotate(5deg);
    }
    100% {
        transform: scale(1.1) rotate(5deg);
    }
}

.page-header h1 {
    font-size: 2rem;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-header h1 i {
    color: #e74c3c;
}

.header-info {
    margin-top: 0.5rem;
}

.info-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.info-badge i {
    font-size: 1rem;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

/* Content Cards */
.content-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

/* Form Styles */
.user-form {
    max-width: 800px;
}

.form-row {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    flex: 1;
}

.form-group.full-width {
    flex: 100%;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e0e6ed;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-family: 'Cairo', sans-serif;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(149, 165, 166, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-start;
    margin-top: 2rem;
}

/* Search Bar */
.search-bar {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-bar input {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 1rem;
    border: 2px solid #e0e6ed;
    border-radius: 25px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.search-bar input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-bar i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
}

/* Table Styles */
.table-container {
    overflow-x: auto;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.data-table th {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: white;
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    border-bottom: 2px solid #e74c3c;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
    text-align: right;
}

.data-table tbody tr:hover {
    background: #f8f9fa;
}

.data-table tbody tr:nth-child(even) {
    background: #f8f9fa;
}

/* Action Buttons in Table */
.action-btn {
    padding: 0.25rem 0.5rem;
    margin: 0 0.25rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.action-btn.edit {
    background: #f39c12;
    color: white;
}

.action-btn.delete {
    background: #e74c3c;
    color: white;
}

.action-btn:hover {
    transform: scale(1.1);
}

/* Reports Grid */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.report-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    border-color: #3498db;
}

.report-card i {
    font-size: 3rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.report-card h3 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.report-card p {
    color: #7f8c8d;
    line-height: 1.6;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1.5rem 2rem;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.close {
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #ecf0f1;
}

.modal-body {
    padding: 2rem;
}

/* Mills Performance Styles */
.mills-performance {
    margin-top: 1rem;
}

.performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.performance-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}

.performance-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.performance-card h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.efficiency-bar {
    background: #e9ecef;
    border-radius: 10px;
    height: 20px;
    margin: 1rem 0;
    overflow: hidden;
    position: relative;
}

.efficiency-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.8s ease;
    position: relative;
}

.efficiency-fill.high {
    background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.efficiency-fill.medium {
    background: linear-gradient(90deg, #f39c12, #e67e22);
}

.efficiency-fill.low {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.efficiency-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 0.9rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Production Statistics */
.production-stats {
    margin-top: 1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-right: 4px solid #3498db;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card:nth-child(1) { border-right-color: #27ae60; }
.stat-card:nth-child(2) { border-right-color: #f39c12; }
.stat-card:nth-child(3) { border-right-color: #e74c3c; }
.stat-card:nth-child(4) { border-right-color: #9b59b6; }

.stat-icon {
    font-size: 2.5rem;
    color: #3498db;
}

.stat-card:nth-child(1) .stat-icon { color: #27ae60; }
.stat-card:nth-child(2) .stat-icon { color: #f39c12; }
.stat-card:nth-child(3) .stat-icon { color: #e74c3c; }
.stat-card:nth-child(4) .stat-icon { color: #9b59b6; }

.stat-info h4 {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
    margin: 0;
}

.stat-info p {
    color: #7f8c8d;
    margin: 0;
    font-size: 0.9rem;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    display: inline-block;
}

.status-active {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-idle {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-maintenance {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Mini Efficiency Bar for Table */
.efficiency-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.mini-efficiency-bar {
    width: 60px;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.mini-efficiency-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.5s ease;
}

.mini-efficiency-fill.high {
    background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.mini-efficiency-fill.medium {
    background: linear-gradient(90deg, #f39c12, #e67e22);
}

.mini-efficiency-fill.low {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

/* Productivity Analysis Styles */
.productivity-analysis {
    margin-top: 1rem;
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.analysis-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}

.analysis-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: #3498db;
}

.analysis-card h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    text-align: center;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.efficiency-comparison,
.cost-breakdown {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.comparison-item,
.cost-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: white;
    border-radius: 8px;
    border-right: 4px solid #3498db;
}

.comparison-item:nth-child(2) {
    border-right-color: #f39c12;
}

.comparison-item:nth-child(3) {
    border-right-color: #27ae60;
}

.cost-item:nth-child(1) {
    border-right-color: #e74c3c;
}

.cost-item:nth-child(2) {
    border-right-color: #9b59b6;
}

.cost-item:nth-child(3) {
    border-right-color: #34495e;
}

.label {
    font-weight: 600;
    color: #2c3e50;
}

.value {
    font-weight: bold;
    color: #3498db;
}

.value.achievement {
    color: #27ae60;
    font-size: 1.1rem;
}

/* Maintenance Schedule Styles */
.maintenance-schedule {
    margin-top: 1rem;
}

.schedule-table-container {
    overflow-x: auto;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.schedule-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    font-size: 0.9rem;
}

.schedule-table th {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: white;
    padding: 0.75rem;
    text-align: right;
    font-weight: 600;
    border-bottom: 2px solid #8e44ad;
}

.schedule-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #ecf0f1;
    text-align: right;
}

.schedule-table tbody tr:hover {
    background: #f8f9fa;
}

.schedule-table tbody tr:nth-child(even) {
    background: #f8f9fa;
}

/* Priority Badges */
.priority-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    display: inline-block;
}

.priority-high {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.priority-medium {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    border: 1px solid #ffeaa7;
}

.priority-low {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border: 1px solid #c3e6cb;
}

/* Maintenance Status Badges */
.maintenance-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    display: inline-block;
}

.maintenance-scheduled {
    background: linear-gradient(135deg, #cce5ff, #b3d9ff);
    color: #004085;
    border: 1px solid #b3d9ff;
}

.maintenance-overdue {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.maintenance-completed {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border: 1px solid #c3e6cb;
}

/* Recipe Management Styles */
.recipe-management {
    margin-top: 1rem;
}

.recipe-header {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    align-items: center;
}

.recipes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.recipe-card {
    background: linear-gradient(135deg, #fff, #f8f9fa);
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    cursor: pointer;
}

.recipe-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: #3498db;
}

.recipe-card h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.recipe-code {
    background: #3498db;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.recipe-description {
    color: #7f8c8d;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.recipe-ingredients {
    margin-bottom: 1rem;
}

.recipe-ingredients h5 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.ingredient-item {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
    border-bottom: 1px solid #ecf0f1;
    font-size: 0.8rem;
}

.ingredient-name {
    color: #2c3e50;
}

.ingredient-percentage {
    color: #3498db;
    font-weight: bold;
}

.recipe-total-weight {
    background: #e8f5e8;
    padding: 0.5rem;
    border-radius: 6px;
    text-align: center;
    font-weight: bold;
    color: #27ae60;
    margin-bottom: 1rem;
}

.recipe-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

/* Recipe Modal Styles */
.recipe-modal-content {
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.recipe-basic-info {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.ingredients-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.total-percentage {
    font-weight: bold;
    color: #2c3e50;
}

.ingredients-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
}

.ingredient-row {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.ingredient-row .form-group {
    margin-bottom: 0;
    flex: 1;
}

.ingredient-row .form-group:last-child {
    flex: 0 0 auto;
}

.remove-ingredient {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.remove-ingredient:hover {
    background: #c0392b;
    transform: scale(1.1);
}

/* Production Planning Styles */
.production-planning {
    margin-top: 1rem;
}

.planning-header {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    align-items: center;
}

.production-queue h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #3498db;
}

.queue-table-container {
    overflow-x: auto;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.queue-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    font-size: 0.9rem;
}

.queue-table th {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 0.75rem;
    text-align: right;
    font-weight: 600;
    border-bottom: 2px solid #2980b9;
}

.queue-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #ecf0f1;
    text-align: right;
}

.queue-table tbody tr:hover {
    background: #f8f9fa;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #27ae60, #2ecc71);
    border-radius: 10px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 0.8rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Production Status Badges */
.production-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    display: inline-block;
}

.status-running {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-paused {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-completed {
    background: linear-gradient(135deg, #cce5ff, #b3d9ff);
    color: #004085;
    border: 1px solid #b3d9ff;
}

.status-pending {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Production Modal Expanded Styles */
.production-modal-content {
    max-width: 1200px;
    max-height: 95vh;
    overflow-y: auto;
    width: 95%;
}

.production-step {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}

.production-step:hover {
    border-color: #3498db;
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.1);
}

.production-step h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #3498db;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.production-step h3 i {
    color: #3498db;
}

/* Recipe Details Expanded */
.recipe-details-expanded {
    margin-top: 1rem;
    padding: 1.5rem;
    background: white;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    display: none;
}

.recipe-details-expanded.active {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.recipe-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.recipe-info-card {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-right: 4px solid #3498db;
}

.recipe-info-card h5 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.ingredients-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.ingredients-table th {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 0.75rem;
    text-align: right;
    font-weight: 600;
}

.ingredients-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #ecf0f1;
    text-align: right;
}

.ingredients-table tbody tr:hover {
    background: #f8f9fa;
}

/* Mills Selection Grid */
.mills-selection {
    margin-top: 1rem;
}

.mills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.mill-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.mill-card:hover {
    border-color: #3498db;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.2);
}

.mill-card.selected {
    border-color: #27ae60;
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
}

.mill-card.unavailable {
    opacity: 0.6;
    cursor: not-allowed;
    background: #f8f9fa;
}

.mill-card h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mill-status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.mill-status-indicator.active {
    background: #27ae60;
    box-shadow: 0 0 5px rgba(39, 174, 96, 0.5);
}

.mill-status-indicator.busy {
    background: #f39c12;
    box-shadow: 0 0 5px rgba(243, 156, 18, 0.5);
}

.mill-status-indicator.maintenance {
    background: #e74c3c;
    box-shadow: 0 0 5px rgba(231, 76, 60, 0.5);
}

.mill-specs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-top: 1rem;
    font-size: 0.9rem;
}

.mill-spec {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.mill-spec .label {
    color: #7f8c8d;
}

.mill-spec .value {
    color: #2c3e50;
    font-weight: 600;
}

/* Material Requirements */
.material-requirements {
    margin-top: 1rem;
}

.requirements-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.requirements-table th {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    padding: 0.75rem;
    text-align: right;
    font-weight: 600;
}

.requirements-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #ecf0f1;
    text-align: right;
}

.requirements-table tbody tr:hover {
    background: #f8f9fa;
}

.availability-status {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.availability-available {
    background: #d4edda;
    color: #155724;
}

.availability-low {
    background: #fff3cd;
    color: #856404;
}

.availability-unavailable {
    background: #f8d7da;
    color: #721c24;
}

/* Production Summary */
.production-summary {
    background: linear-gradient(135deg, #e8f5e8, #d4edda);
    border: 2px solid #27ae60;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 2rem 0;
}

.production-summary h3 {
    color: #155724;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: white;
    border-radius: 8px;
    border-right: 4px solid #27ae60;
}

.summary-item .label {
    color: #7f8c8d;
    font-weight: 600;
}

.summary-item .value {
    color: #2c3e50;
    font-weight: bold;
}

/* Enhanced Form Actions */
.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: bold;
}

/* Production Planning Details */
.production-planning-details {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    margin-top: 1rem;
}

/* Requirements Summary */
.requirements-summary {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    border-right: 4px solid #3498db;
}

.summary-row {
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.summary-row:last-child {
    margin-bottom: 0;
}

/* Production Log Styles */
.production-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: linear-gradient(135deg, #fff, #f8f9fa);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-right: 4px solid #3498db;
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.summary-card:nth-child(1) { border-right-color: #3498db; }
.summary-card:nth-child(2) { border-right-color: #27ae60; }
.summary-card:nth-child(3) { border-right-color: #f39c12; }
.summary-card:nth-child(4) { border-right-color: #e74c3c; }

.summary-icon {
    font-size: 2.5rem;
    color: #3498db;
    min-width: 60px;
    text-align: center;
}

.summary-card:nth-child(1) .summary-icon { color: #3498db; }
.summary-card:nth-child(2) .summary-icon { color: #27ae60; }
.summary-card:nth-child(3) .summary-icon { color: #f39c12; }
.summary-card:nth-child(4) .summary-icon { color: #e74c3c; }

.summary-info h3 {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
    margin: 0;
}

.summary-info p {
    color: #7f8c8d;
    margin: 0;
    font-size: 0.9rem;
}

/* Filters Section */
.filters-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    margin-top: 1rem;
}

/* Product-wise Details */
.product-wise-container {
    margin-top: 1rem;
}

.product-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.product-card:hover {
    border-color: #3498db;
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.1);
}

.product-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.product-header h4 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.product-toggle {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.product-toggle.expanded {
    transform: rotate(180deg);
}

.product-content {
    padding: 1.5rem;
    display: none;
}

.product-content.expanded {
    display: block;
    animation: slideDown 0.3s ease;
}

.product-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.product-stat {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-right: 3px solid #3498db;
}

.product-stat h5 {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
}

.product-stat p {
    color: #7f8c8d;
    margin: 0;
    font-size: 0.9rem;
}

.production-history-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.production-history-table th {
    background: #34495e;
    color: white;
    padding: 0.75rem;
    text-align: right;
    font-weight: 600;
    border-bottom: 2px solid #2c3e50;
}

.production-history-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #ecf0f1;
    text-align: right;
}

.production-history-table tbody tr:hover {
    background: #f8f9fa;
}

.production-history-table tbody tr:nth-child(even) {
    background: #f8f9fa;
}

/* Production Details Modal */
.production-details-modal {
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
}

.production-detail-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
    border-right: 4px solid #3498db;
}

.production-detail-section h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #dee2e6;
}

.detail-label {
    font-weight: 600;
    color: #7f8c8d;
}

.detail-value {
    color: #2c3e50;
    font-weight: bold;
}

/* Progress Indicators */
.progress-indicator {
    margin: 1rem 0;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.progress-bar-container {
    width: 100%;
    height: 25px;
    background: #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

.progress-bar-fill {
    height: 100%;
    border-radius: 12px;
    transition: width 0.8s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-bar-fill.high {
    background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.progress-bar-fill.medium {
    background: linear-gradient(90deg, #f39c12, #e67e22);
}

.progress-bar-fill.low {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.progress-text {
    color: white;
    font-weight: bold;
    font-size: 0.9rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Recipes and Production Planning Page Styles */
.production-planning {
    margin-top: 1rem;
}

.planning-header {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.production-queue-section {
    margin-bottom: 2rem;
}

.production-queue-section h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.production-stats-section {
    margin-top: 2rem;
}

.production-stats-section h4 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: linear-gradient(135deg, #fff, #f8f9fa);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-right: 4px solid #3498db;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card:nth-child(1) { border-right-color: #3498db; }
.stat-card:nth-child(2) { border-right-color: #f39c12; }
.stat-card:nth-child(3) { border-right-color: #27ae60; }
.stat-card:nth-child(4) { border-right-color: #e74c3c; }

.stat-icon {
    font-size: 2.5rem;
    color: #3498db;
    min-width: 60px;
    text-align: center;
}

.stat-card:nth-child(1) .stat-icon { color: #3498db; }
.stat-card:nth-child(2) .stat-icon { color: #f39c12; }
.stat-card:nth-child(3) .stat-icon { color: #27ae60; }
.stat-card:nth-child(4) .stat-icon { color: #e74c3c; }

.stat-info h3 {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
    margin: 0;
}

.stat-info p {
    color: #7f8c8d;
    margin: 0;
    font-size: 0.9rem;
}

/* Enhanced Recipe Management Styles */
.recipe-management {
    margin-top: 1rem;
}

.recipe-header {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.recipes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

/* Enhanced Production Queue Table */
#productionQueueTable {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

#productionQueueTable th {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: white;
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    border-bottom: 2px solid #2c3e50;
}

#productionQueueTable td {
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
    text-align: right;
    vertical-align: middle;
}

#productionQueueTable tbody tr:hover {
    background: #f8f9fa;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

#productionQueueTable tbody tr:nth-child(even) {
    background: #f8f9fa;
}

/* Progress bars in production table */
.production-progress {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.progress-bar-mini {
    width: 60px;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill-mini {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-fill-mini.high {
    background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.progress-fill-mini.medium {
    background: linear-gradient(90deg, #f39c12, #e67e22);
}

.progress-fill-mini.low {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
}

/* Fullscreen Mode Styles */
.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: white;
    overflow-y: auto;
}

.fullscreen-mode .sidebar {
    display: none !important;
}

.fullscreen-mode .main-content {
    margin-right: 0 !important;
    width: 100% !important;
    max-width: none !important;
}

.fullscreen-mode .page {
    padding: 2rem;
    height: calc(100vh - 4rem);
    overflow-y: auto;
}

.fullscreen-mode .page-header {
    position: sticky;
    top: 0;
    background: white;
    z-index: 100;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
    border-bottom: 2px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.fullscreen-mode .content-card {
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.fullscreen-mode .table-container {
    max-height: calc(100vh - 300px);
    overflow-y: auto;
}

.fullscreen-toggle {
    transition: all 0.3s ease;
}

.fullscreen-toggle:hover {
    transform: scale(1.05);
}

.fullscreen-toggle.active {
    background: #e74c3c !important;
    border-color: #c0392b !important;
}

.fullscreen-toggle.active i {
    transform: rotate(45deg);
}

/* Enhanced fullscreen experience */
.fullscreen-mode .recipes-grid {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 2rem;
}

.fullscreen-mode .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.fullscreen-mode .production-summary-cards {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.fullscreen-mode .data-table {
    font-size: 1rem;
}

.fullscreen-mode .data-table th,
.fullscreen-mode .data-table td {
    padding: 1rem;
}

/* Fullscreen exit button */
.fullscreen-exit {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 10000;
    background: rgba(231, 76, 60, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
}

.fullscreen-exit:hover {
    background: rgba(192, 57, 43, 0.9);
    transform: scale(1.1);
}

/* Fullscreen logo watermark */
.fullscreen-mode::before {
    content: '';
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background-image: url('logo-mini.svg');
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.3;
    z-index: 1000;
    pointer-events: none;
}

/* Responsive Design for Production Modal */
@media (max-width: 768px) {
    .production-modal-content {
        width: 98%;
        margin: 1% auto;
        max-height: 98vh;
    }

    .recipe-info-grid,
    .mills-grid,
    .summary-grid {
        grid-template-columns: 1fr;
    }

    .mill-specs {
        grid-template-columns: 1fr;
    }

    .production-step {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .ingredients-table,
    .requirements-table {
        font-size: 0.8rem;
    }

    .ingredients-table th,
    .ingredients-table td,
    .requirements-table th,
    .requirements-table td {
        padding: 0.5rem;
    }

    .production-summary-cards {
        grid-template-columns: 1fr;
    }

    .product-stats {
        grid-template-columns: 1fr;
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }

    .planning-header,
    .recipe-header {
        flex-direction: column;
        gap: 0.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .recipes-grid {
        grid-template-columns: 1fr;
    }

    #productionQueueTable {
        font-size: 0.8rem;
    }

    #productionQueueTable th,
    #productionQueueTable td {
        padding: 0.5rem;
    }

    /* Fullscreen responsive adjustments */
    .fullscreen-mode .page {
        padding: 1rem;
    }

    .fullscreen-mode .recipes-grid {
        grid-template-columns: 1fr;
    }

    .fullscreen-mode .stats-grid {
        grid-template-columns: 1fr;
    }

    .fullscreen-mode .production-summary-cards {
        grid-template-columns: 1fr;
    }

    .fullscreen-mode .data-table {
        font-size: 0.8rem;
    }

    .fullscreen-mode .data-table th,
    .fullscreen-mode .data-table td {
        padding: 0.5rem;
    }

    .fullscreen-exit {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    /* Logo responsive adjustments */
    .company-logo {
        max-width: 150px;
    }

    .page-logo-img {
        width: 60px;
    }

    .page-title-section {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .page-title-section h1 {
        font-size: 1.5rem;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }

    .main-content {
        margin-right: 0;
        padding: 1rem;
    }

    .form-row {
        flex-direction: column;
        gap: 1rem;
    }

    .header-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .reports-grid {
        grid-template-columns: 1fr;
    }

    .performance-grid,
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }
}
