<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 80 80" width="80" height="80">
  <defs>
    <!-- Warm Gradient -->
    <linearGradient id="warmGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff9a56;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff6b9d;stop-opacity:1" />
    </linearGradient>

    <!-- Text Gradient -->
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5a3c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d4a574;stop-opacity:1" />
    </linearGradient>

    <!-- Mill Gradient -->
    <linearGradient id="millGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6c5ce7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a29bfe;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Mill and Text Design -->
  <g transform="translate(40, 40)">
    <!-- Small Mill Icon with new colors -->
    <g transform="translate(-25, 0)">
      <path d="M -8 2 Q -8 1 -7 1 L 7 1 Q 8 1 8 2 L 8 9 Q 8 11 6 11 L -6 11 Q -8 11 -8 9 Z" fill="url(#millGrad)"/>
      <path d="M -6 -3 Q -6 -4 -5 -4 L 5 -4 Q 6 -4 6 -3 L 6 1 L -6 1 Z" fill="#8b5a3c"/>
      <circle cx="9" cy="-5" r="2.5" fill="#ff9a56"/>
      <circle cx="0" cy="0" r="4" fill="#ff6b9d" opacity="0.3"/>
      <circle cx="0" cy="0" r="2" fill="#8b5a3c"/>

      <!-- Colorful particles -->
      <circle cx="-10" cy="5" r="0.8" fill="#ff9a56" opacity="0.8"/>
      <circle cx="10" cy="3" r="0.6" fill="#ff6b9d" opacity="0.7"/>
      <circle cx="-8" cy="8" r="0.5" fill="#a29bfe" opacity="0.6"/>
    </g>

    <!-- Company Name -->
    <text x="8" y="-5" font-family="Cairo, sans-serif" font-size="18" font-weight="400" fill="url(#textGradient)" text-anchor="middle">مطحنة</text>

    <!-- Subtitle -->
    <text x="8" y="8" font-family="Cairo, sans-serif" font-size="8" font-weight="300" fill="#6c5ce7" text-anchor="middle" opacity="0.9">إدارة المخزون</text>

    <!-- Elegant Underline -->
    <path d="M -15 15 Q 8 17 31 15" stroke="url(#warmGrad)" stroke-width="1.2" fill="none" opacity="0.7"/>
  </g>
</svg>
