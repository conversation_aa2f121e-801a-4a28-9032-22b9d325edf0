// Database Management
class SpiceMillDB {
    constructor() {
        this.initializeData();
    }

    initializeData() {
        // Initialize with sample data if localStorage is empty
        if (!localStorage.getItem('rawMaterials')) {
            const sampleRawMaterials = [
                { id: 1, code: 'RM001', name: 'فلفل أسود', weight: 25, quantity: 100, date: '2024-07-15' },
                { id: 2, code: 'RM002', name: 'كمون', weight: 20, quantity: 80, date: '2024-07-16' },
                { id: 3, code: 'RM003', name: 'كزبرة', weight: 30, quantity: 120, date: '2024-07-17' }
            ];
            this.saveRawMaterials(sampleRawMaterials);
        }

        if (!localStorage.getItem('finishedProducts')) {
            const sampleFinishedProducts = [
                { id: 1, code: 'FP001', name: 'بهارات مشكلة', weight: 15, quantity: 50, date: '2024-07-18' },
                { id: 2, code: 'FP002', name: 'كاري', weight: 10, quantity: 30, date: '2024-07-19' }
            ];
            this.saveFinishedProducts(sampleFinishedProducts);
        }

        if (!localStorage.getItem('mixtures')) {
            const sampleMixtures = [
                { id: 1, code: 'MX001', name: 'خلطة البهارات الحارة', components: 'فلفل أسود + كمون + كزبرة', weight: 25, date: '2024-07-20' },
                { id: 2, code: 'MX002', name: 'خلطة الكاري الهندي', components: 'كمون + كزبرة + كركم', weight: 20, date: '2024-07-21' }
            ];
            this.saveMixtures(sampleMixtures);
        }

        if (!localStorage.getItem('mills')) {
            const sampleMills = [
                {
                    id: 1,
                    code: 'ML001',
                    name: 'مطحنة الرئيسية',
                    type: 'مطحنة ثقيلة',
                    capacity: 150,
                    efficiency: 85,
                    workingHours: 8,
                    dailyProduction: 1020,
                    operatingCost: 45,
                    status: 'نشطة',
                    lastMaintenance: '2024-07-10',
                    nextMaintenance: '2024-10-10',
                    maintenanceType: 'صيانة دورية',
                    priority: 'متوسطة'
                },
                {
                    id: 2,
                    code: 'ML002',
                    name: 'مطحنة الكمون والكزبرة',
                    type: 'مطحنة متوسطة',
                    capacity: 100,
                    efficiency: 92,
                    workingHours: 10,
                    dailyProduction: 920,
                    operatingCost: 35,
                    status: 'نشطة',
                    lastMaintenance: '2024-07-15',
                    nextMaintenance: '2024-10-15',
                    maintenanceType: 'فحص شامل',
                    priority: 'منخفضة'
                },
                {
                    id: 3,
                    code: 'ML003',
                    name: 'مطحنة الفلفل الأسود',
                    type: 'مطحنة خفيفة',
                    capacity: 75,
                    efficiency: 78,
                    workingHours: 0,
                    dailyProduction: 0,
                    operatingCost: 25,
                    status: 'صيانة',
                    lastMaintenance: '2024-07-20',
                    nextMaintenance: '2024-08-20',
                    maintenanceType: 'إصلاح عطل',
                    priority: 'عالية'
                },
                {
                    id: 4,
                    code: 'ML004',
                    name: 'مطحنة الخلطات الخاصة',
                    type: 'مطحنة ثقيلة',
                    capacity: 200,
                    efficiency: 88,
                    workingHours: 6,
                    dailyProduction: 1056,
                    operatingCost: 55,
                    status: 'نشطة',
                    lastMaintenance: '2024-07-12',
                    nextMaintenance: '2024-09-12',
                    maintenanceType: 'تغيير قطع',
                    priority: 'عالية'
                },
                {
                    id: 5,
                    code: 'ML005',
                    name: 'مطحنة الهيل والقرفة',
                    type: 'مطحنة خفيفة',
                    capacity: 80,
                    efficiency: 90,
                    workingHours: 7,
                    dailyProduction: 504,
                    operatingCost: 30,
                    status: 'نشطة',
                    lastMaintenance: '2024-07-25',
                    nextMaintenance: '2024-11-25',
                    maintenanceType: 'صيانة دورية',
                    priority: 'منخفضة'
                }
            ];
            this.saveMills(sampleMills);
        }

        if (!localStorage.getItem('recipes')) {
            const sampleRecipes = [
                {
                    id: 1,
                    code: 'RCP001',
                    name: 'خلطة البهارات الحارة',
                    description: 'خلطة مميزة من البهارات الحارة للأطباق الشرقية',
                    totalWeight: 100,
                    ingredients: [
                        { name: 'فلفل أسود', percentage: 30, weight: 30 },
                        { name: 'فلفل أحمر', percentage: 25, weight: 25 },
                        { name: 'كمون', percentage: 20, weight: 20 },
                        { name: 'كزبرة', percentage: 15, weight: 15 },
                        { name: 'هيل', percentage: 10, weight: 10 }
                    ],
                    createdDate: '2024-07-15'
                },
                {
                    id: 2,
                    code: 'RCP002',
                    name: 'خلطة الكاري الهندي',
                    description: 'خلطة كاري أصلية بالطعم الهندي التقليدي',
                    totalWeight: 100,
                    ingredients: [
                        { name: 'كركم', percentage: 35, weight: 35 },
                        { name: 'كمون', percentage: 20, weight: 20 },
                        { name: 'كزبرة', percentage: 20, weight: 20 },
                        { name: 'فلفل أحمر', percentage: 15, weight: 15 },
                        { name: 'حلبة', percentage: 10, weight: 10 }
                    ],
                    createdDate: '2024-07-16'
                },
                {
                    id: 3,
                    code: 'RCP003',
                    name: 'بهارات اللحم المشوي',
                    description: 'خلطة مثالية للحوم المشوية والكباب',
                    totalWeight: 100,
                    ingredients: [
                        { name: 'فلفل أسود', percentage: 25, weight: 25 },
                        { name: 'بابريكا', percentage: 20, weight: 20 },
                        { name: 'كمون', percentage: 20, weight: 20 },
                        { name: 'ثوم مجفف', percentage: 15, weight: 15 },
                        { name: 'أوريجانو', percentage: 10, weight: 10 },
                        { name: 'ملح', percentage: 10, weight: 10 }
                    ],
                    createdDate: '2024-07-17'
                }
            ];
            this.saveRecipes(sampleRecipes);
        }

        if (!localStorage.getItem('productionQueue')) {
            const sampleProduction = [
                {
                    id: 1,
                    recipeId: 1,
                    recipeName: 'خلطة البهارات الحارة',
                    millId: 1,
                    millName: 'مطحنة الرئيسية',
                    requestedQuantity: 50,
                    producedQuantity: 35,
                    estimatedTime: 2.5,
                    status: 'running',
                    startDate: '2024-07-20'
                },
                {
                    id: 2,
                    recipeId: 2,
                    recipeName: 'خلطة الكاري الهندي',
                    millId: 2,
                    millName: 'مطحنة الكمون والكزبرة',
                    requestedQuantity: 30,
                    producedQuantity: 30,
                    estimatedTime: 1.8,
                    status: 'completed',
                    startDate: '2024-07-19'
                }
            ];
            this.saveProductionQueue(sampleProduction);
        }

        if (!localStorage.getItem('userInfo')) {
            const defaultUser = {
                name: '',
                email: '',
                phone: '',
                role: '',
                address: ''
            };
            this.saveUserInfo(defaultUser);
        }
    }

    // Raw Materials
    getRawMaterials() {
        return JSON.parse(localStorage.getItem('rawMaterials')) || [];
    }

    saveRawMaterials(data) {
        localStorage.setItem('rawMaterials', JSON.stringify(data));
    }

    addRawMaterial(material) {
        const materials = this.getRawMaterials();
        material.id = Date.now();
        materials.push(material);
        this.saveRawMaterials(materials);
        return material;
    }

    updateRawMaterial(id, updatedMaterial) {
        const materials = this.getRawMaterials();
        const index = materials.findIndex(m => m.id === id);
        if (index !== -1) {
            materials[index] = { ...materials[index], ...updatedMaterial };
            this.saveRawMaterials(materials);
            return materials[index];
        }
        return null;
    }

    deleteRawMaterial(id) {
        const materials = this.getRawMaterials();
        const filtered = materials.filter(m => m.id !== id);
        this.saveRawMaterials(filtered);
        return true;
    }

    // Finished Products
    getFinishedProducts() {
        return JSON.parse(localStorage.getItem('finishedProducts')) || [];
    }

    saveFinishedProducts(data) {
        localStorage.setItem('finishedProducts', JSON.stringify(data));
    }

    addFinishedProduct(product) {
        const products = this.getFinishedProducts();
        product.id = Date.now();
        products.push(product);
        this.saveFinishedProducts(products);
        return product;
    }

    updateFinishedProduct(id, updatedProduct) {
        const products = this.getFinishedProducts();
        const index = products.findIndex(p => p.id === id);
        if (index !== -1) {
            products[index] = { ...products[index], ...updatedProduct };
            this.saveFinishedProducts(products);
            return products[index];
        }
        return null;
    }

    deleteFinishedProduct(id) {
        const products = this.getFinishedProducts();
        const filtered = products.filter(p => p.id !== id);
        this.saveFinishedProducts(filtered);
        return true;
    }

    // Mixtures
    getMixtures() {
        return JSON.parse(localStorage.getItem('mixtures')) || [];
    }

    saveMixtures(data) {
        localStorage.setItem('mixtures', JSON.stringify(data));
    }

    addMixture(mixture) {
        const mixtures = this.getMixtures();
        mixture.id = Date.now();
        mixtures.push(mixture);
        this.saveMixtures(mixtures);
        return mixture;
    }

    updateMixture(id, updatedMixture) {
        const mixtures = this.getMixtures();
        const index = mixtures.findIndex(m => m.id === id);
        if (index !== -1) {
            mixtures[index] = { ...mixtures[index], ...updatedMixture };
            this.saveMixtures(mixtures);
            return mixtures[index];
        }
        return null;
    }

    deleteMixture(id) {
        const mixtures = this.getMixtures();
        const filtered = mixtures.filter(m => m.id !== id);
        this.saveMixtures(filtered);
        return true;
    }

    // Mills
    getMills() {
        return JSON.parse(localStorage.getItem('mills')) || [];
    }

    saveMills(data) {
        localStorage.setItem('mills', JSON.stringify(data));
    }

    addMill(mill) {
        const mills = this.getMills();
        mill.id = Date.now();
        mills.push(mill);
        this.saveMills(mills);
        return mill;
    }

    updateMill(id, updatedMill) {
        const mills = this.getMills();
        const index = mills.findIndex(m => m.id === id);
        if (index !== -1) {
            mills[index] = { ...mills[index], ...updatedMill };
            this.saveMills(mills);
            return mills[index];
        }
        return null;
    }

    deleteMill(id) {
        const mills = this.getMills();
        const filtered = mills.filter(m => m.id !== id);
        this.saveMills(filtered);
        return true;
    }

    // User Info
    getUserInfo() {
        return JSON.parse(localStorage.getItem('userInfo')) || {};
    }

    saveUserInfo(userInfo) {
        localStorage.setItem('userInfo', JSON.stringify(userInfo));
    }

    // Recipes
    getRecipes() {
        return JSON.parse(localStorage.getItem('recipes')) || [];
    }

    saveRecipes(data) {
        localStorage.setItem('recipes', JSON.stringify(data));
    }

    addRecipe(recipe) {
        const recipes = this.getRecipes();
        recipe.id = Date.now();
        recipe.createdDate = new Date().toISOString().split('T')[0];
        recipes.push(recipe);
        this.saveRecipes(recipes);
        return recipe;
    }

    updateRecipe(id, updatedRecipe) {
        const recipes = this.getRecipes();
        const index = recipes.findIndex(r => r.id === id);
        if (index !== -1) {
            recipes[index] = { ...recipes[index], ...updatedRecipe };
            this.saveRecipes(recipes);
            return recipes[index];
        }
        return null;
    }

    deleteRecipe(id) {
        const recipes = this.getRecipes();
        const filtered = recipes.filter(r => r.id !== id);
        this.saveRecipes(filtered);
        return true;
    }

    // Production Queue
    getProductionQueue() {
        return JSON.parse(localStorage.getItem('productionQueue')) || [];
    }

    saveProductionQueue(data) {
        localStorage.setItem('productionQueue', JSON.stringify(data));
    }

    addProductionJob(job) {
        const queue = this.getProductionQueue();
        job.id = Date.now();
        job.startDate = new Date().toISOString().split('T')[0];
        job.status = 'pending';
        job.producedQuantity = 0;
        queue.push(job);
        this.saveProductionQueue(queue);
        return job;
    }

    updateProductionJob(id, updatedJob) {
        const queue = this.getProductionQueue();
        const index = queue.findIndex(j => j.id === id);
        if (index !== -1) {
            queue[index] = { ...queue[index], ...updatedJob };
            this.saveProductionQueue(queue);
            return queue[index];
        }
        return null;
    }

    deleteProductionJob(id) {
        const queue = this.getProductionQueue();
        const filtered = queue.filter(j => j.id !== id);
        this.saveProductionQueue(filtered);
        return true;
    }
}

// Initialize database
const db = new SpiceMillDB();

// Global Data Storage (now using database)
let rawMaterials = db.getRawMaterials();
let finishedProducts = db.getFinishedProducts();
let mixtures = db.getMixtures();
let mills = db.getMills();
let recipes = db.getRecipes();
let productionQueue = db.getProductionQueue();

// Helper function for Arabic date formatting (Gregorian calendar)
function formatArabicDate(date = new Date()) {
    return date.toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory'
    });
}

// Helper function to format stored date strings to Arabic
function formatStoredDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return formatArabicDate(date);
}

// Navigation functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize navigation
    const navItems = document.querySelectorAll('.nav-item');
    const pages = document.querySelectorAll('.page');

    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetPage = this.getAttribute('data-page');
            showPage(targetPage);
        });
    });

    // Load initial data
    loadUserInfo();
    loadRawMaterials();
    loadFinishedProducts();
    loadMixtures();
    loadMills();
    loadRecipes();
    loadProductionQueue();
    loadProductionLog();
    loadRecipesProductionPage();

    // Form submission handlers
    setupFormHandlers();
});

// Load data into forms and tables
function loadUserInfo() {
    const userInfo = db.getUserInfo();
    if (userInfo) {
        document.getElementById('userName').value = userInfo.name || '';
        document.getElementById('userEmail').value = userInfo.email || '';
        document.getElementById('userPhone').value = userInfo.phone || '';
        document.getElementById('userRole').value = userInfo.role || '';
        document.getElementById('userAddress').value = userInfo.address || '';
    }
}

function loadRawMaterials() {
    const tbody = document.getElementById('rawMaterialsBody');
    tbody.innerHTML = '';
    
    rawMaterials.forEach(item => {
        const row = `
            <tr>
                <td>${item.code}</td>
                <td>${item.name}</td>
                <td>${item.weight}</td>
                <td>${item.quantity}</td>
                <td>${formatStoredDate(item.date)}</td>
                <td>
                    <button class="action-btn edit" onclick="editItem('raw-material', ${item.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="action-btn delete" onclick="deleteItem('raw-material', ${item.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

function loadFinishedProducts() {
    const tbody = document.getElementById('finishedProductsBody');
    tbody.innerHTML = '';
    
    finishedProducts.forEach(item => {
        const row = `
            <tr>
                <td>${item.code}</td>
                <td>${item.name}</td>
                <td>${item.weight}</td>
                <td>${item.quantity}</td>
                <td>${formatStoredDate(item.date)}</td>
                <td>
                    <button class="action-btn edit" onclick="editItem('finished-product', ${item.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="action-btn delete" onclick="deleteItem('finished-product', ${item.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

function loadMixtures() {
    const tbody = document.getElementById('mixturesBody');
    tbody.innerHTML = '';

    mixtures.forEach(item => {
        const row = `
            <tr>
                <td>${item.code}</td>
                <td>${item.name}</td>
                <td>${item.components}</td>
                <td>${item.weight}</td>
                <td>${formatStoredDate(item.date)}</td>
                <td>
                    <button class="action-btn edit" onclick="editItem('mixture', ${item.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="action-btn delete" onclick="deleteItem('mixture', ${item.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

function loadMills() {
    const tbody = document.getElementById('millsBody');
    tbody.innerHTML = '';

    mills.forEach(item => {
        const statusClass = getStatusClass(item.status);
        const row = `
            <tr>
                <td>${item.code}</td>
                <td>${item.name}</td>
                <td>${item.type}</td>
                <td>${item.capacity}</td>
                <td>
                    <div class="efficiency-display">
                        <span>${item.efficiency}%</span>
                        <div class="mini-efficiency-bar">
                            <div class="mini-efficiency-fill ${getEfficiencyClass(item.efficiency)}"
                                 style="width: ${item.efficiency}%"></div>
                        </div>
                    </div>
                </td>
                <td>${item.workingHours || 0}</td>
                <td>${item.dailyProduction || 0}</td>
                <td>${item.operatingCost || 0}</td>
                <td><span class="status-badge ${statusClass}">${item.status}</span></td>
                <td>${item.lastMaintenance}</td>
                <td>${item.nextMaintenance || 'غير محدد'}</td>
                <td>
                    <button class="action-btn edit" onclick="editItem('mill', ${item.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="action-btn delete" onclick="deleteItem('mill', ${item.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });

    // Load performance cards and statistics
    loadMillsPerformance();
    loadMillsStatistics();
    loadMaintenanceSchedule();
    loadProductivityAnalysis();
}

function loadRecipes() {
    const recipesGrid = document.getElementById('recipesGrid');
    if (!recipesGrid) return;

    recipesGrid.innerHTML = '';

    recipes.forEach(recipe => {
        const card = `
            <div class="recipe-card" onclick="viewRecipe(${recipe.id})">
                <h4>
                    ${recipe.name}
                    <span class="recipe-code">${recipe.code}</span>
                </h4>
                <p class="recipe-description">${recipe.description}</p>
                <div class="recipe-total-weight">
                    الوزن الإجمالي: ${recipe.totalWeight} كجم
                </div>
                <div class="recipe-ingredients">
                    <h5>المكونات (${recipe.ingredients.length}):</h5>
                    ${recipe.ingredients.slice(0, 3).map(ing => `
                        <div class="ingredient-item">
                            <span class="ingredient-name">${ing.name}</span>
                            <span class="ingredient-percentage">${ing.percentage}%</span>
                        </div>
                    `).join('')}
                    ${recipe.ingredients.length > 3 ? '<div class="ingredient-item"><span>...</span></div>' : ''}
                </div>
                <div class="recipe-actions">
                    <button class="action-btn edit" onclick="event.stopPropagation(); editRecipe(${recipe.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="action-btn view" onclick="event.stopPropagation(); printSingleRecipe(${recipe.id})">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="action-btn delete" onclick="event.stopPropagation(); deleteRecipe(${recipe.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `;
        recipesGrid.innerHTML += card;
    });
}

function loadProductionQueue() {
    const tbody = document.getElementById('productionQueueBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    productionQueue.forEach(job => {
        const progress = job.requestedQuantity > 0 ?
            Math.round((job.producedQuantity / job.requestedQuantity) * 100) : 0;
        const statusClass = getProductionStatusClass(job.status);
        const statusText = getProductionStatusText(job.status);

        const row = `
            <tr>
                <td>${job.recipeName}</td>
                <td>${job.millName}</td>
                <td>${job.requestedQuantity} كجم</td>
                <td>${job.producedQuantity} كجم</td>
                <td>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progress}%">
                            <span class="progress-text">${progress}%</span>
                        </div>
                    </div>
                </td>
                <td><span class="production-status ${statusClass}">${statusText}</span></td>
                <td>
                    <button class="action-btn edit" onclick="updateProduction(${job.id})">
                        <i class="fas fa-edit"></i> تحديث
                    </button>
                    <button class="action-btn delete" onclick="cancelProduction(${job.id})">
                        <i class="fas fa-stop"></i> إيقاف
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

// Load Recipes and Production Planning Page
function loadRecipesProductionPage() {
    // Load recipes in the new page
    loadRecipesInNewPage();
    // Load production queue in the new page
    loadProductionQueueInNewPage();
    // Load production statistics
    loadProductionStatistics();
}

function loadRecipesInNewPage() {
    const recipesGrid = document.querySelector('#recipes-production .recipes-grid');
    if (!recipesGrid) return;

    recipesGrid.innerHTML = '';

    recipes.forEach(recipe => {
        const card = `
            <div class="recipe-card" onclick="viewRecipeDetails(${recipe.id})">
                <div class="recipe-header">
                    <h4>${recipe.name}</h4>
                    <span class="recipe-code">${recipe.code}</span>
                </div>
                <div class="recipe-info">
                    <p><strong>الوزن الأساسي:</strong> ${recipe.totalWeight} كجم</p>
                    <p><strong>عدد المكونات:</strong> ${recipe.ingredients.length}</p>
                    <p><strong>تاريخ الإنشاء:</strong> ${formatStoredDate(recipe.createdDate)}</p>
                </div>
                <div class="recipe-ingredients">
                    ${recipe.ingredients.slice(0, 3).map(ingredient =>
                        `<div class="ingredient-item">
                            <span>${ingredient.name}</span>
                            <span>${ingredient.percentage}%</span>
                        </div>`
                    ).join('')}
                    ${recipe.ingredients.length > 3 ? '<div class="ingredient-item"><span>...</span></div>' : ''}
                </div>
                <div class="recipe-actions">
                    <button class="action-btn edit" onclick="event.stopPropagation(); editRecipe(${recipe.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="action-btn view" onclick="event.stopPropagation(); printSingleRecipe(${recipe.id})">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="action-btn delete" onclick="event.stopPropagation(); deleteRecipe(${recipe.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `;
        recipesGrid.innerHTML += card;
    });
}

function loadProductionQueueInNewPage() {
    const tbody = document.querySelector('#recipes-production #productionQueueBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    productionQueue.forEach(job => {
        const progress = job.requestedQuantity > 0 ?
            Math.round((job.producedQuantity / job.requestedQuantity) * 100) : 0;
        const statusClass = getProductionStatusClass(job.status);
        const statusText = getProductionStatusText(job.status);

        const row = `
            <tr>
                <td>${job.recipeName}</td>
                <td>${job.millName}</td>
                <td>${job.requestedQuantity} كجم</td>
                <td>${job.producedQuantity || 0} كجم</td>
                <td>
                    <div class="production-progress">
                        <div class="progress-bar-mini">
                            <div class="progress-fill-mini ${getProgressClass(progress)}" style="width: ${progress}%"></div>
                        </div>
                        <span>${progress}%</span>
                    </div>
                </td>
                <td>${job.estimatedTime || 0} ساعة</td>
                <td>${Math.round(job.estimatedCost || 0)} ريال</td>
                <td>${formatStoredDate(job.startDate)}</td>
                <td>${job.priority || 'متوسطة'}</td>
                <td><span class="production-status ${statusClass}">${statusText}</span></td>
                <td>
                    <button class="action-btn edit" onclick="editProduction(${job.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="action-btn delete" onclick="cancelProduction(${job.id})">
                        <i class="fas fa-stop"></i> إيقاف
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

function loadProductionStatistics() {
    const totalJobs = productionQueue.length;
    const runningJobs = productionQueue.filter(job => job.status === 'running').length;
    const completedJobs = productionQueue.filter(job => job.status === 'completed').length;
    const pendingJobs = productionQueue.filter(job => job.status === 'pending').length;

    const totalJobsElement = document.getElementById('totalJobs');
    const runningJobsElement = document.getElementById('runningJobs');
    const completedJobsElement = document.getElementById('completedJobs');
    const pendingJobsElement = document.getElementById('pendingJobs');

    if (totalJobsElement) totalJobsElement.textContent = totalJobs;
    if (runningJobsElement) runningJobsElement.textContent = runningJobs;
    if (completedJobsElement) completedJobsElement.textContent = completedJobs;
    if (pendingJobsElement) pendingJobsElement.textContent = pendingJobs;
}

// Production Log Functions
function loadProductionLog() {
    if (!document.getElementById('productionLogBody')) return;

    loadProductionSummary();
    loadProductionFilters();
    loadProductionLogTable();
    loadProductWiseDetails();
}

function loadProductionSummary() {
    const totalJobs = productionQueue.length;
    const completedJobs = productionQueue.filter(job => job.status === 'completed').length;
    const totalProduced = productionQueue.reduce((sum, job) => sum + (job.producedQuantity || 0), 0);
    const totalCost = productionQueue.reduce((sum, job) => {
        const actualTime = job.actualTime || job.estimatedTime || 0;
        return sum + (actualTime * (job.estimatedCost || 0) / (job.estimatedTime || 1));
    }, 0);

    document.getElementById('totalProductionJobs').textContent = totalJobs;
    document.getElementById('completedJobs').textContent = completedJobs;
    document.getElementById('totalProducedWeight').textContent = totalProduced.toFixed(1);
    document.getElementById('totalProductionCost').textContent = Math.round(totalCost);
}

function loadProductionFilters() {
    // Populate recipe filter
    const recipeFilter = document.getElementById('filterRecipe');
    if (recipeFilter) {
        recipeFilter.innerHTML = '<option value="">جميع الوصفات</option>';
        const uniqueRecipes = [...new Set(productionQueue.map(job => job.recipeName))];
        uniqueRecipes.forEach(recipeName => {
            recipeFilter.innerHTML += `<option value="${recipeName}">${recipeName}</option>`;
        });
    }

    // Populate mill filter
    const millFilter = document.getElementById('filterMill');
    if (millFilter) {
        millFilter.innerHTML = '<option value="">جميع المطاحين</option>';
        const uniqueMills = [...new Set(productionQueue.map(job => job.millName))];
        uniqueMills.forEach(millName => {
            millFilter.innerHTML += `<option value="${millName}">${millName}</option>`;
        });
    }
}

function loadProductionLogTable(filteredData = null) {
    const tbody = document.getElementById('productionLogBody');
    if (!tbody) return;

    const data = filteredData || productionQueue;
    tbody.innerHTML = '';

    data.forEach(job => {
        const progress = job.requestedQuantity > 0 ?
            Math.round((job.producedQuantity / job.requestedQuantity) * 100) : 0;
        const statusClass = getProductionStatusClass(job.status);
        const statusText = getProductionStatusText(job.status);
        const actualTime = job.actualTime || (job.status === 'completed' ? job.estimatedTime : 0);
        const endDate = job.endDate || (job.status === 'completed' ? job.startDate : '-');

        const row = `
            <tr>
                <td>${job.id}</td>
                <td>${job.recipeName}</td>
                <td>${job.millName}</td>
                <td>${job.requestedQuantity} كجم</td>
                <td>${job.producedQuantity || 0} كجم</td>
                <td>
                    <div class="progress-indicator">
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill ${getProgressClass(progress)}" style="width: ${progress}%">
                                <span class="progress-text">${progress}%</span>
                            </div>
                        </div>
                    </div>
                </td>
                <td>${formatStoredDate(job.startDate)}</td>
                <td><span class="production-status ${statusClass}">${statusText}</span></td>
                <td>
                    <button class="action-btn view" onclick="viewProductionDetails(${job.id})">
                        <i class="fas fa-eye"></i> تفاصيل
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

function getProgressClass(progress) {
    if (progress >= 80) return 'high';
    if (progress >= 50) return 'medium';
    return 'low';
}

// Helper functions for mills
function getStatusClass(status) {
    switch(status) {
        case 'نشطة': return 'status-active';
        case 'متوقفة': return 'status-idle';
        case 'صيانة': return 'status-maintenance';
        default: return 'status-idle';
    }
}

function getEfficiencyClass(efficiency) {
    if (efficiency >= 80) return 'high';
    if (efficiency >= 60) return 'medium';
    return 'low';
}

function loadMillsPerformance() {
    const performanceGrid = document.getElementById('performanceGrid');
    performanceGrid.innerHTML = '';

    mills.forEach(mill => {
        const efficiencyClass = getEfficiencyClass(mill.efficiency);
        const card = `
            <div class="performance-card">
                <h4>${mill.name}</h4>
                <p><strong>النوع:</strong> ${mill.type}</p>
                <p><strong>السعة:</strong> ${mill.capacity} كجم/ساعة</p>
                <div class="efficiency-bar">
                    <div class="efficiency-fill ${efficiencyClass}" style="width: ${mill.efficiency}%">
                        <span class="efficiency-text">${mill.efficiency}%</span>
                    </div>
                </div>
                <p><strong>الحالة:</strong> <span class="status-badge ${getStatusClass(mill.status)}">${mill.status}</span></p>
            </div>
        `;
        performanceGrid.innerHTML += card;
    });
}

function loadMillsStatistics() {
    const activeMills = mills.filter(m => m.status === 'نشطة').length;
    const idleMills = mills.filter(m => m.status === 'متوقفة').length;
    const maintenanceMills = mills.filter(m => m.status === 'صيانة').length;
    const avgEfficiency = mills.length > 0 ?
        Math.round(mills.reduce((sum, m) => sum + m.efficiency, 0) / mills.length) : 0;

    const totalWorkingHours = mills.reduce((sum, m) => sum + (m.workingHours || 0), 0);
    const totalProduction = mills.reduce((sum, m) => sum + (m.dailyProduction || 0), 0);
    const totalCost = mills.reduce((sum, m) => sum + ((m.workingHours || 0) * (m.operatingCost || 0)), 0);

    // Count upcoming maintenance (within next 30 days)
    const today = new Date();
    const upcomingMaintenance = mills.filter(m => {
        if (!m.nextMaintenance) return false;
        const maintenanceDate = new Date(m.nextMaintenance);
        const diffTime = maintenanceDate - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays <= 30 && diffDays >= 0;
    }).length;

    document.getElementById('activeMills').textContent = activeMills;
    document.getElementById('idleMills').textContent = idleMills;
    document.getElementById('maintenanceMills').textContent = maintenanceMills;
    document.getElementById('avgEfficiency').textContent = avgEfficiency + '%';
    document.getElementById('totalWorkingHours').textContent = totalWorkingHours;
    document.getElementById('totalProduction').textContent = totalProduction;
    document.getElementById('totalCost').textContent = Math.round(totalCost);
    document.getElementById('upcomingMaintenance').textContent = upcomingMaintenance;
}

function loadProductivityAnalysis() {
    const activeMills = mills.filter(m => m.status === 'نشطة');

    // Calculate actual vs expected production
    const actualProduction = activeMills.reduce((sum, m) => sum + (m.dailyProduction || 0), 0);
    const expectedProduction = activeMills.reduce((sum, m) => sum + (m.capacity * (m.workingHours || 0)), 0);
    const achievementRate = expectedProduction > 0 ? Math.round((actualProduction / expectedProduction) * 100) : 0;

    // Calculate cost breakdown
    const electricityCost = Math.round(activeMills.reduce((sum, m) => sum + ((m.workingHours || 0) * (m.operatingCost || 0) * 0.4), 0));
    const maintenanceCost = Math.round(mills.length * 150); // Average maintenance cost per mill
    const laborCost = Math.round(activeMills.length * 200); // Average labor cost per active mill

    document.getElementById('actualProduction').textContent = actualProduction + ' كجم';
    document.getElementById('expectedProduction').textContent = expectedProduction + ' كجم';
    document.getElementById('achievementRate').textContent = achievementRate + '%';
    document.getElementById('electricityCost').textContent = electricityCost + ' ريال';
    document.getElementById('maintenanceCost').textContent = maintenanceCost + ' ريال';
    document.getElementById('laborCost').textContent = laborCost + ' ريال';
}

function loadMaintenanceSchedule() {
    const tbody = document.getElementById('maintenanceScheduleBody');
    tbody.innerHTML = '';

    mills.forEach(mill => {
        const priorityClass = getPriorityClass(mill.priority);
        const maintenanceStatus = getMaintenanceStatus(mill.nextMaintenance);
        const statusClass = getMaintenanceStatusClass(maintenanceStatus);

        const row = `
            <tr>
                <td>${mill.name}</td>
                <td>${mill.maintenanceType || 'صيانة دورية'}</td>
                <td>${formatStoredDate(mill.lastMaintenance)}</td>
                <td>${mill.nextMaintenance ? formatStoredDate(mill.nextMaintenance) : 'غير محدد'}</td>
                <td><span class="priority-badge ${priorityClass}">${mill.priority || 'متوسطة'}</span></td>
                <td><span class="maintenance-status ${statusClass}">${maintenanceStatus}</span></td>
                <td>
                    <button class="action-btn edit" onclick="scheduleMaintenance(${mill.id})">
                        <i class="fas fa-calendar-plus"></i> جدولة
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

function getPriorityClass(priority) {
    switch(priority) {
        case 'عالية': return 'priority-high';
        case 'متوسطة': return 'priority-medium';
        case 'منخفضة': return 'priority-low';
        default: return 'priority-medium';
    }
}

function getMaintenanceStatus(nextMaintenance) {
    if (!nextMaintenance) return 'غير مجدولة';

    const today = new Date();
    const maintenanceDate = new Date(nextMaintenance);
    const diffTime = maintenanceDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return 'متأخرة';
    if (diffDays <= 7) return 'قريبة';
    return 'مجدولة';
}

function getMaintenanceStatusClass(status) {
    switch(status) {
        case 'متأخرة': return 'maintenance-overdue';
        case 'قريبة': return 'maintenance-scheduled';
        case 'مجدولة': return 'maintenance-scheduled';
        default: return 'maintenance-completed';
    }
}

function scheduleMaintenance(millId) {
    alert(`جدولة صيانة للمطحنة رقم ${millId} - هذه الوظيفة قيد التطوير`);
}

// Functions for Recipes and Production Page
function showProductionHistory() {
    // Switch to production log page
    showPage('production-log');
}

function loadDrafts() {
    const drafts = JSON.parse(localStorage.getItem('productionDrafts')) || [];
    if (drafts.length === 0) {
        alert('لا توجد مسودات محفوظة.');
        return;
    }

    let draftsText = 'المسودات المحفوظة:\n\n';
    drafts.forEach((draft, index) => {
        draftsText += `${index + 1}. ${draft.recipeName} - ${draft.requestedQuantity} كجم\n`;
        draftsText += `   المطحنة: ${draft.millName}\n`;
        draftsText += `   تاريخ البدء: ${draft.startDate}\n\n`;
    });

    alert(draftsText);
}

function editProduction(jobId) {
    const job = productionQueue.find(j => j.id === jobId);
    if (!job) {
        alert('مهمة الإنتاج غير موجودة!');
        return;
    }

    // Open production modal with existing data
    openProductionModal();

    // Fill form with existing data
    document.getElementById('selectedRecipe').value = job.recipeId;
    document.getElementById('selectedMill').value = job.millId;
    document.getElementById('productionQuantity').value = job.requestedQuantity;
    document.getElementById('estimatedTime').value = job.estimatedTime;
    document.getElementById('estimatedCost').value = job.estimatedCost;
    document.getElementById('priority').value = job.priority;
    document.getElementById('startDate').value = job.startDate;
    document.getElementById('productionNotes').value = job.notes || '';

    // Store job ID for update
    document.getElementById('productionForm').setAttribute('data-edit-id', jobId);

    // Change form title and button text
    document.querySelector('#productionModal .modal-header h2').textContent = 'تعديل مهمة الإنتاج';
    document.querySelector('#productionForm button[type="submit"]').innerHTML = '<i class="fas fa-save"></i> حفظ التعديل';
}

function cancelProduction(jobId) {
    if (confirm('هل أنت متأكد من إيقاف هذه المهمة؟')) {
        const jobIndex = productionQueue.findIndex(j => j.id === jobId);
        if (jobIndex !== -1) {
            productionQueue[jobIndex].status = 'cancelled';
            db.updateProductionQueue(productionQueue);
            loadProductionQueue();
            loadProductionQueueInNewPage();
            loadProductionStatistics();
            alert('تم إيقاف المهمة بنجاح!');
        }
    }
}

// Update production form handler to support editing
function updateProductionFormHandler() {
    const form = document.getElementById('productionForm');
    const originalHandler = form.onsubmit;

    form.onsubmit = function(e) {
        e.preventDefault();
        const editId = this.getAttribute('data-edit-id');

        if (editId) {
            // Update existing job
            const formData = new FormData(this);
            const jobIndex = productionQueue.findIndex(j => j.id === parseInt(editId));

            if (jobIndex !== -1) {
                productionQueue[jobIndex].requestedQuantity = parseFloat(formData.get('productionQuantity'));
                productionQueue[jobIndex].estimatedTime = parseFloat(formData.get('estimatedTime'));
                productionQueue[jobIndex].estimatedCost = parseFloat(formData.get('estimatedCost'));
                productionQueue[jobIndex].priority = formData.get('priority');
                productionQueue[jobIndex].startDate = formData.get('startDate');
                productionQueue[jobIndex].notes = formData.get('productionNotes');

                db.updateProductionQueue(productionQueue);
                loadProductionQueue();
                loadProductionQueueInNewPage();
                loadProductionStatistics();
                closeProductionModal();
                alert('تم تعديل المهمة بنجاح!');
            }
        } else {
            // Call original handler for new jobs
            originalHandler.call(this, e);
        }
    };
}

function filterProductionLog() {
    const recipeFilter = document.getElementById('filterRecipe').value;
    const millFilter = document.getElementById('filterMill').value;
    const statusFilter = document.getElementById('filterStatus').value;
    const dateFromFilter = document.getElementById('filterDateFrom').value;
    const dateToFilter = document.getElementById('filterDateTo').value;

    let filteredData = productionQueue.filter(job => {
        let matches = true;

        if (recipeFilter && job.recipeName !== recipeFilter) matches = false;
        if (millFilter && job.millName !== millFilter) matches = false;
        if (statusFilter && job.status !== statusFilter) matches = false;

        if (dateFromFilter) {
            const jobDate = new Date(job.startDate);
            const fromDate = new Date(dateFromFilter);
            if (jobDate < fromDate) matches = false;
        }

        if (dateToFilter) {
            const jobDate = new Date(job.startDate);
            const toDate = new Date(dateToFilter);
            if (jobDate > toDate) matches = false;
        }

        return matches;
    });

    loadProductionLogTable(filteredData);
    loadProductWiseDetails(filteredData);
}

function clearFilters() {
    document.getElementById('filterRecipe').value = '';
    document.getElementById('filterMill').value = '';
    document.getElementById('filterStatus').value = '';
    document.getElementById('filterDateFrom').value = '';
    document.getElementById('filterDateTo').value = '';

    loadProductionLogTable();
    loadProductWiseDetails();
}

function loadProductWiseDetails(filteredData = null) {
    const container = document.getElementById('productWiseDetails');
    if (!container) return;

    const data = filteredData || productionQueue;

    // Group by recipe
    const productGroups = {};
    data.forEach(job => {
        if (!productGroups[job.recipeName]) {
            productGroups[job.recipeName] = [];
        }
        productGroups[job.recipeName].push(job);
    });

    container.innerHTML = '';

    Object.keys(productGroups).forEach(recipeName => {
        const jobs = productGroups[recipeName];
        const totalRequested = jobs.reduce((sum, job) => sum + job.requestedQuantity, 0);
        const totalProduced = jobs.reduce((sum, job) => sum + (job.producedQuantity || 0), 0);
        const completedJobs = jobs.filter(job => job.status === 'completed').length;
        const avgEfficiency = jobs.length > 0 ?
            Math.round(jobs.reduce((sum, job) => {
                const progress = job.requestedQuantity > 0 ?
                    (job.producedQuantity || 0) / job.requestedQuantity * 100 : 0;
                return sum + progress;
            }, 0) / jobs.length) : 0;

        const productCard = `
            <div class="product-card">
                <div class="product-header" onclick="toggleProductDetails('${recipeName.replace(/\s+/g, '-')}')">
                    <h4>
                        <i class="fas fa-flask"></i>
                        ${recipeName}
                        <span style="font-size: 0.8rem; opacity: 0.8;">(${jobs.length} مهمة)</span>
                    </h4>
                    <i class="fas fa-chevron-down product-toggle" id="toggle-${recipeName.replace(/\s+/g, '-')}"></i>
                </div>
                <div class="product-content" id="content-${recipeName.replace(/\s+/g, '-')}">
                    <div class="product-stats">
                        <div class="product-stat">
                            <h5>${totalRequested}</h5>
                            <p>إجمالي المطلوب (كجم)</p>
                        </div>
                        <div class="product-stat">
                            <h5>${totalProduced.toFixed(1)}</h5>
                            <p>إجمالي المنتج (كجم)</p>
                        </div>
                        <div class="product-stat">
                            <h5>${completedJobs}</h5>
                            <p>مهام مكتملة</p>
                        </div>
                        <div class="product-stat">
                            <h5>${avgEfficiency}%</h5>
                            <p>متوسط الكفاءة</p>
                        </div>
                    </div>

                    <h5>سجل الإنتاج:</h5>
                    <table class="production-history-table">
                        <thead>
                            <tr>
                                <th>رقم المهمة</th>
                                <th>المطحنة</th>
                                <th>الكمية المطلوبة</th>
                                <th>الكمية المنتجة</th>
                                <th>التقدم</th>
                                <th>تاريخ البدء</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${jobs.map(job => {
                                const progress = job.requestedQuantity > 0 ?
                                    Math.round((job.producedQuantity || 0) / job.requestedQuantity * 100) : 0;
                                const statusClass = getProductionStatusClass(job.status);
                                const statusText = getProductionStatusText(job.status);

                                return `
                                    <tr>
                                        <td>${job.id}</td>
                                        <td>${job.millName}</td>
                                        <td>${job.requestedQuantity} كجم</td>
                                        <td>${job.producedQuantity || 0} كجم</td>
                                        <td>${progress}%</td>
                                        <td>${formatStoredDate(job.startDate)}</td>
                                        <td><span class="production-status ${statusClass}">${statusText}</span></td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        container.innerHTML += productCard;
    });
}

function toggleProductDetails(productId) {
    const content = document.getElementById(`content-${productId}`);
    const toggle = document.getElementById(`toggle-${productId}`);

    if (content.classList.contains('expanded')) {
        content.classList.remove('expanded');
        toggle.classList.remove('expanded');
    } else {
        content.classList.add('expanded');
        toggle.classList.add('expanded');
    }
}

function getProductionStatusClass(status) {
    switch(status) {
        case 'running': return 'status-running';
        case 'paused': return 'status-paused';
        case 'completed': return 'status-completed';
        case 'pending': return 'status-pending';
        default: return 'status-pending';
    }
}

function getProductionStatusText(status) {
    switch(status) {
        case 'running': return 'قيد التشغيل';
        case 'paused': return 'متوقف مؤقتاً';
        case 'completed': return 'مكتمل';
        case 'pending': return 'في الانتظار';
        default: return 'غير محدد';
    }
}

// Recipe Management Functions
function openRecipeModal() {
    document.getElementById('recipeModal').style.display = 'block';
    addIngredient(); // Add first ingredient row
}

function closeRecipeModal() {
    document.getElementById('recipeModal').style.display = 'none';
    document.getElementById('recipeForm').reset();
    document.getElementById('ingredientsList').innerHTML = '';
    document.getElementById('totalPercentage').textContent = '0%';

    // Reset edit mode
    document.getElementById('recipeForm').removeAttribute('data-edit-id');
    document.querySelector('#recipeModal .modal-header h2').textContent = 'إنشاء وصفة جديدة';
    document.querySelector('#recipeForm button[type="submit"]').innerHTML = '<i class="fas fa-save"></i> حفظ الوصفة';
}

function addIngredient() {
    const ingredientsList = document.getElementById('ingredientsList');
    const ingredientCount = ingredientsList.children.length;

    const ingredientRow = document.createElement('div');
    ingredientRow.className = 'ingredient-row';
    ingredientRow.innerHTML = `
        <div class="form-group">
            <label>المكون</label>
            <select name="ingredient_${ingredientCount}" required onchange="updateTotalPercentage()">
                <option value="">اختر المكون</option>
                ${rawMaterials.map(material => `<option value="${material.name}">${material.name}</option>`).join('')}
            </select>
        </div>
        <div class="form-group">
            <label>النسبة المئوية (%)</label>
            <input type="number" name="percentage_${ingredientCount}" min="0" max="100" step="0.1"
                   required onchange="updateTotalPercentage()" oninput="updateTotalPercentage()">
        </div>
        <div class="form-group">
            <label>الوزن (كجم)</label>
            <input type="number" name="weight_${ingredientCount}" min="0" step="0.1" readonly>
        </div>
        <button type="button" class="remove-ingredient" onclick="removeIngredient(this)">
            <i class="fas fa-times"></i>
        </button>
    `;

    ingredientsList.appendChild(ingredientRow);
}

function removeIngredient(button) {
    button.parentElement.remove();
    updateTotalPercentage();
}

function updateTotalPercentage() {
    const percentageInputs = document.querySelectorAll('input[name^="percentage_"]');
    const totalWeightInput = document.getElementById('totalWeight');
    let totalPercentage = 0;

    percentageInputs.forEach(input => {
        const value = parseFloat(input.value) || 0;
        totalPercentage += value;

        // Update corresponding weight
        const index = input.name.split('_')[1];
        const weightInput = document.querySelector(`input[name="weight_${index}"]`);
        const totalWeight = parseFloat(totalWeightInput.value) || 0;

        if (weightInput && totalWeight > 0) {
            weightInput.value = ((value / 100) * totalWeight).toFixed(2);
        }
    });

    document.getElementById('totalPercentage').textContent = totalPercentage.toFixed(1) + '%';

    // Change color based on total percentage
    const totalElement = document.getElementById('totalPercentage');
    if (totalPercentage === 100) {
        totalElement.style.color = '#27ae60';
    } else if (totalPercentage > 100) {
        totalElement.style.color = '#e74c3c';
    } else {
        totalElement.style.color = '#f39c12';
    }
}

function viewRecipe(recipeId) {
    const recipe = recipes.find(r => r.id === recipeId);
    if (!recipe) return;

    alert(`عرض تفاصيل الوصفة: ${recipe.name}\n\nالمكونات:\n${recipe.ingredients.map(ing => `${ing.name}: ${ing.percentage}%`).join('\n')}`);
}

function editRecipe(recipeId) {
    const recipe = recipes.find(r => r.id === recipeId);
    if (!recipe) {
        alert('الوصفة غير موجودة!');
        return;
    }

    // Open recipe modal in edit mode
    document.getElementById('recipeModal').style.display = 'block';

    // Fill form with existing data
    document.getElementById('recipeName').value = recipe.name;
    document.getElementById('recipeCode').value = recipe.code;
    document.getElementById('recipeDescription').value = recipe.description;
    document.getElementById('totalWeight').value = recipe.totalWeight;

    // Clear existing ingredients
    document.getElementById('ingredientsList').innerHTML = '';

    // Add existing ingredients
    recipe.ingredients.forEach((ingredient, index) => {
        addIngredient();
        const ingredientRow = document.querySelectorAll('.ingredient-row')[index];
        const ingredientSelect = ingredientRow.querySelector(`select[name="ingredient_${index}"]`);
        const percentageInput = ingredientRow.querySelector(`input[name="percentage_${index}"]`);
        const weightInput = ingredientRow.querySelector(`input[name="weight_${index}"]`);

        ingredientSelect.value = ingredient.name;
        percentageInput.value = ingredient.percentage;
        weightInput.value = ingredient.weight;
    });

    updateTotalPercentage();

    // Store recipe ID for update
    document.getElementById('recipeForm').setAttribute('data-edit-id', recipeId);

    // Change form title and button text
    document.querySelector('#recipeModal .modal-header h2').textContent = 'تعديل الوصفة';
    document.querySelector('#recipeForm button[type="submit"]').innerHTML = '<i class="fas fa-save"></i> حفظ التعديل';
}

function printSingleRecipe(recipeId) {
    const recipe = recipes.find(r => r.id === recipeId);
    if (!recipe) {
        alert('الوصفة غير موجودة!');
        return;
    }

    // Create print window
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>وصفة ${recipe.name}</title>
            <meta charset="UTF-8">
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>
                body {
                    font-family: 'Cairo', Arial, sans-serif;
                    direction: rtl;
                    margin: 0;
                    padding: 20px;
                    line-height: 1.6;
                    color: #333;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 3px solid #3498db;
                    padding-bottom: 20px;
                }
                h1 {
                    color: #2c3e50;
                    font-size: 28px;
                    margin: 0 0 10px 0;
                }
                .recipe-code {
                    color: #7f8c8d;
                    font-size: 18px;
                    margin: 0;
                }
                .recipe-info {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                    margin-bottom: 25px;
                    border-right: 4px solid #3498db;
                }
                .info-grid {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    gap: 15px;
                    margin-bottom: 15px;
                }
                .info-item {
                    background: white;
                    padding: 15px;
                    border-radius: 8px;
                }
                .info-label {
                    font-weight: bold;
                    color: #7f8c8d;
                    display: block;
                    margin-bottom: 5px;
                }
                .info-value {
                    color: #2c3e50;
                    font-size: 1.1rem;
                }
                .description {
                    background: white;
                    padding: 15px;
                    border-radius: 8px;
                    grid-column: 1 / -1;
                }
                .ingredients-section {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                    border-right: 4px solid #27ae60;
                }
                .ingredients-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 15px;
                    background: white;
                }
                .ingredients-table th,
                .ingredients-table td {
                    border: 1px solid #ddd;
                    padding: 12px;
                    text-align: right;
                }
                .ingredients-table th {
                    background: #27ae60;
                    color: white;
                    font-weight: bold;
                }
                .ingredients-table tbody tr:nth-child(even) {
                    background: #f8f9fa;
                }
                .total-row {
                    background: #e8f5e8 !important;
                    font-weight: bold;
                }
                .footer {
                    margin-top: 40px;
                    text-align: center;
                    color: #7f8c8d;
                    font-size: 12px;
                    border-top: 1px solid #ecf0f1;
                    padding-top: 20px;
                }
                @media print {
                    body { margin: 0; }
                    @page {
                        margin: 1cm;
                        size: A4;
                    }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>${recipe.name}</h1>
                <p class="recipe-code">كود الوصفة: ${recipe.code}</p>
            </div>

            <div class="recipe-info">
                <h3>معلومات الوصفة</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">الوزن الأساسي:</span>
                        <span class="info-value">${recipe.totalWeight} كجم</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">عدد المكونات:</span>
                        <span class="info-value">${recipe.ingredients.length}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">تاريخ الإنشاء:</span>
                        <span class="info-value">${formatStoredDate(recipe.createdDate)}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">تاريخ الطباعة:</span>
                        <span class="info-value">${formatArabicDate()}</span>
                    </div>
                    ${recipe.description ? `
                    <div class="description">
                        <span class="info-label">الوصف:</span>
                        <span class="info-value">${recipe.description}</span>
                    </div>
                    ` : ''}
                </div>
            </div>

            <div class="ingredients-section">
                <h3>المكونات والنسب</h3>
                <table class="ingredients-table">
                    <thead>
                        <tr>
                            <th>المكون</th>
                            <th>النسبة المئوية</th>
                            <th>الوزن (كجم)</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${recipe.ingredients.map(ingredient => `
                            <tr>
                                <td>${ingredient.name}</td>
                                <td>${ingredient.percentage}%</td>
                                <td>${ingredient.weight}</td>
                            </tr>
                        `).join('')}
                        <tr class="total-row">
                            <td><strong>المجموع</strong></td>
                            <td><strong>${recipe.ingredients.reduce((sum, ing) => sum + ing.percentage, 0)}%</strong></td>
                            <td><strong>${recipe.totalWeight} كجم</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة مخزون مطحنة</p>
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();

    // Wait for content to load then print
    setTimeout(() => {
        printWindow.print();
    }, 1000);
}

function deleteRecipe(recipeId) {
    if (confirm('هل أنت متأكد من حذف هذه الوصفة؟')) {
        db.deleteRecipe(recipeId);
        recipes = db.getRecipes();
        loadRecipes();
        alert('تم حذف الوصفة بنجاح!');
    }
}

// Production Management Functions
function openProductionModal() {
    document.getElementById('productionModal').style.display = 'block';

    // Set default start date to today
    document.getElementById('startDate').value = new Date().toISOString().split('T')[0];

    // Populate recipe options
    const recipeSelect = document.getElementById('selectedRecipe');
    recipeSelect.innerHTML = '<option value="">اختر الوصفة المطلوب إنتاجها</option>';
    recipes.forEach(recipe => {
        recipeSelect.innerHTML += `<option value="${recipe.id}">${recipe.name} (${recipe.code})</option>`;
    });

    // Load mills grid
    loadMillsGrid();
}

function closeProductionModal() {
    document.getElementById('productionModal').style.display = 'none';
    document.getElementById('productionForm').reset();
    document.getElementById('recipeDetails').innerHTML = '';
    document.getElementById('materialRequirements').innerHTML = '';
    document.getElementById('selectedMill').value = '';
    clearMillSelection();
    clearSummary();

    // Reset edit mode
    document.getElementById('productionForm').removeAttribute('data-edit-id');
    document.querySelector('#productionModal .modal-header h2').textContent = 'تخطيط إنتاج جديد';
    document.querySelector('#productionForm button[type="submit"]').innerHTML = '<i class="fas fa-play"></i> بدء الإنتاج';
}

function loadMillsGrid() {
    const millsGrid = document.getElementById('millsGrid');
    millsGrid.innerHTML = '';

    mills.forEach(mill => {
        const isAvailable = mill.status === 'نشطة';
        const statusClass = mill.status === 'نشطة' ? 'active' :
                           mill.status === 'صيانة' ? 'maintenance' : 'busy';

        const millCard = `
            <div class="mill-card ${isAvailable ? '' : 'unavailable'}"
                 onclick="${isAvailable ? `selectMill(${mill.id})` : ''}"
                 data-mill-id="${mill.id}">
                <h4>
                    <span class="mill-status-indicator ${statusClass}"></span>
                    ${mill.name}
                </h4>
                <p><strong>النوع:</strong> ${mill.type}</p>
                <div class="mill-specs">
                    <div class="mill-spec">
                        <span class="label">السعة:</span>
                        <span class="value">${mill.capacity} كجم/ساعة</span>
                    </div>
                    <div class="mill-spec">
                        <span class="label">الكفاءة:</span>
                        <span class="value">${mill.efficiency}%</span>
                    </div>
                    <div class="mill-spec">
                        <span class="label">ساعات التشغيل:</span>
                        <span class="value">${mill.workingHours || 0} ساعة/يوم</span>
                    </div>
                    <div class="mill-spec">
                        <span class="label">التكلفة:</span>
                        <span class="value">${mill.operatingCost || 0} ريال/ساعة</span>
                    </div>
                </div>
                <div class="mill-status">
                    <strong>الحالة:</strong>
                    <span class="status-badge ${getStatusClass(mill.status)}">${mill.status}</span>
                </div>
            </div>
        `;
        millsGrid.innerHTML += millCard;
    });
}

function selectMill(millId) {
    // Clear previous selection
    clearMillSelection();

    // Select new mill
    const millCard = document.querySelector(`[data-mill-id="${millId}"]`);
    if (millCard && !millCard.classList.contains('unavailable')) {
        millCard.classList.add('selected');
        document.getElementById('selectedMill').value = millId;
        updateProductionCalculations();
    }
}

function clearMillSelection() {
    document.querySelectorAll('.mill-card').forEach(card => {
        card.classList.remove('selected');
    });
}

function updateRecipeDetails() {
    const recipeId = parseInt(document.getElementById('selectedRecipe').value);
    const recipe = recipes.find(r => r.id === recipeId);
    const recipeDetails = document.getElementById('recipeDetails');

    if (!recipe) {
        recipeDetails.innerHTML = '';
        recipeDetails.classList.remove('active');
        return;
    }

    recipeDetails.innerHTML = `
        <div class="recipe-info-grid">
            <div class="recipe-info-card">
                <h5>معلومات الوصفة</h5>
                <p><strong>الاسم:</strong> ${recipe.name}</p>
                <p><strong>الكود:</strong> ${recipe.code}</p>
                <p><strong>الوصف:</strong> ${recipe.description}</p>
                <p><strong>الوزن الأساسي:</strong> ${recipe.totalWeight} كجم</p>
                <p><strong>تاريخ الإنشاء:</strong> ${formatStoredDate(recipe.createdDate)}</p>
            </div>
            <div class="recipe-info-card">
                <h5>إحصائيات الوصفة</h5>
                <p><strong>عدد المكونات:</strong> ${recipe.ingredients.length}</p>
                <p><strong>أعلى نسبة:</strong> ${Math.max(...recipe.ingredients.map(i => i.percentage))}%</p>
                <p><strong>أقل نسبة:</strong> ${Math.min(...recipe.ingredients.map(i => i.percentage))}%</p>
                <p><strong>متوسط النسبة:</strong> ${(recipe.ingredients.reduce((sum, i) => sum + i.percentage, 0) / recipe.ingredients.length).toFixed(1)}%</p>
            </div>
        </div>

        <h5>تفاصيل المكونات</h5>
        <table class="ingredients-table">
            <thead>
                <tr>
                    <th>المكون</th>
                    <th>النسبة المئوية</th>
                    <th>الوزن الأساسي (كجم)</th>
                    <th>الوزن المطلوب (كجم)</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
                ${recipe.ingredients.map(ingredient => {
                    const requiredWeight = calculateRequiredWeight(ingredient, recipe);
                    const availability = checkMaterialAvailability(ingredient.name, requiredWeight);
                    return `
                        <tr>
                            <td>${ingredient.name}</td>
                            <td>${ingredient.percentage}%</td>
                            <td>${ingredient.weight}</td>
                            <td id="required-${ingredient.name.replace(/\s+/g, '-')}">${requiredWeight.toFixed(2)}</td>
                            <td><span class="availability-status ${availability.class}">${availability.text}</span></td>
                        </tr>
                    `;
                }).join('')}
            </tbody>
        </table>
    `;

    recipeDetails.classList.add('active');
    updateProductionCalculations();
}

function calculateRequiredWeight(ingredient, recipe) {
    const quantity = parseFloat(document.getElementById('productionQuantity').value) || 0;
    if (quantity === 0) return 0;

    const multiplier = quantity / recipe.totalWeight;
    return ingredient.weight * multiplier;
}

function checkMaterialAvailability(materialName, requiredWeight) {
    const material = rawMaterials.find(m => m.name === materialName);

    if (!material) {
        return { class: 'availability-unavailable', text: 'غير متوفر' };
    }

    const availableWeight = material.quantity * material.weight;

    if (availableWeight >= requiredWeight) {
        return { class: 'availability-available', text: 'متوفر' };
    } else if (availableWeight > 0) {
        return { class: 'availability-low', text: 'كمية قليلة' };
    } else {
        return { class: 'availability-unavailable', text: 'غير متوفر' };
    }
}

function updateProductionCalculations() {
    const quantity = parseFloat(document.getElementById('productionQuantity').value) || 0;
    const millId = parseInt(document.getElementById('selectedMill').value);
    const recipeId = parseInt(document.getElementById('selectedRecipe').value);

    const mill = mills.find(m => m.id === millId);
    const recipe = recipes.find(r => r.id === recipeId);

    if (quantity > 0 && mill && recipe) {
        // Calculate estimated time
        const estimatedHours = (quantity / mill.capacity).toFixed(1);
        document.getElementById('estimatedTime').value = estimatedHours;

        // Calculate estimated cost
        const estimatedCost = (parseFloat(estimatedHours) * (mill.operatingCost || 0)).toFixed(2);
        document.getElementById('estimatedCost').value = estimatedCost;

        // Update material requirements
        updateMaterialRequirements(recipe, quantity);

        // Update summary
        updateProductionSummary(recipe, mill, quantity, estimatedHours, estimatedCost);

        // Update required weights in recipe details if visible
        if (document.getElementById('recipeDetails').classList.contains('active')) {
            recipe.ingredients.forEach(ingredient => {
                const requiredWeightElement = document.getElementById(`required-${ingredient.name.replace(/\s+/g, '-')}`);
                if (requiredWeightElement) {
                    const requiredWeight = calculateRequiredWeight(ingredient, recipe);
                    requiredWeightElement.textContent = requiredWeight.toFixed(2);
                }
            });
        }
    } else {
        document.getElementById('estimatedTime').value = '';
        document.getElementById('estimatedCost').value = '';
        document.getElementById('materialRequirements').innerHTML = '';
        clearSummary();
    }
}

function updateMaterialRequirements(recipe, quantity) {
    const materialRequirements = document.getElementById('materialRequirements');

    if (!recipe || quantity <= 0) {
        materialRequirements.innerHTML = '';
        return;
    }

    const multiplier = quantity / recipe.totalWeight;
    let totalCost = 0;
    let allAvailable = true;

    const requirementsHTML = `
        <h5>متطلبات المواد للإنتاج</h5>
        <table class="requirements-table">
            <thead>
                <tr>
                    <th>المادة الخام</th>
                    <th>الكمية المطلوبة (كجم)</th>
                    <th>الكمية المتوفرة (كجم)</th>
                    <th>التكلفة المقدرة (ريال)</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
                ${recipe.ingredients.map(ingredient => {
                    const requiredWeight = ingredient.weight * multiplier;
                    const material = rawMaterials.find(m => m.name === ingredient.name);
                    const availableWeight = material ? (material.quantity * material.weight) : 0;
                    const estimatedCost = requiredWeight * 10; // تكلفة تقديرية 10 ريال/كجم
                    totalCost += estimatedCost;

                    const availability = checkMaterialAvailability(ingredient.name, requiredWeight);
                    if (availability.class === 'availability-unavailable') {
                        allAvailable = false;
                    }

                    return `
                        <tr>
                            <td>${ingredient.name}</td>
                            <td>${requiredWeight.toFixed(2)}</td>
                            <td>${availableWeight.toFixed(2)}</td>
                            <td>${estimatedCost.toFixed(2)}</td>
                            <td><span class="availability-status ${availability.class}">${availability.text}</span></td>
                        </tr>
                    `;
                }).join('')}
            </tbody>
        </table>

        <div class="requirements-summary">
            <div class="summary-row">
                <strong>التكلفة الإجمالية للمواد: ${totalCost.toFixed(2)} ريال</strong>
            </div>
            <div class="summary-row">
                <strong>حالة التوفر: ${allAvailable ?
                    '<span class="availability-available">جميع المواد متوفرة</span>' :
                    '<span class="availability-unavailable">بعض المواد غير متوفرة</span>'}</strong>
            </div>
        </div>
    `;

    materialRequirements.innerHTML = requirementsHTML;
}

function updateProductionSummary(recipe, mill, quantity, estimatedHours, estimatedCost) {
    document.getElementById('summaryRecipe').textContent = `${recipe.name} (${recipe.code})`;
    document.getElementById('summaryMill').textContent = mill.name;
    document.getElementById('summaryQuantity').textContent = `${quantity} كجم`;
    document.getElementById('summaryTime').textContent = `${estimatedHours} ساعة`;
    document.getElementById('summaryCost').textContent = `${estimatedCost} ريال`;

    const priority = document.getElementById('priority').value;
    document.getElementById('summaryPriority').textContent = priority || '-';
}

function clearSummary() {
    document.getElementById('summaryRecipe').textContent = '-';
    document.getElementById('summaryMill').textContent = '-';
    document.getElementById('summaryQuantity').textContent = '-';
    document.getElementById('summaryTime').textContent = '-';
    document.getElementById('summaryCost').textContent = '-';
    document.getElementById('summaryPriority').textContent = '-';
}

function updateProduction(jobId) {
    const job = productionQueue.find(j => j.id === jobId);
    if (!job) return;

    const newQuantity = prompt(`تحديث الكمية المنتجة للوصفة: ${job.recipeName}\nالكمية الحالية: ${job.producedQuantity} كجم\nأدخل الكمية الجديدة:`, job.producedQuantity);

    if (newQuantity !== null && !isNaN(newQuantity)) {
        const updatedQuantity = parseFloat(newQuantity);
        if (updatedQuantity >= 0 && updatedQuantity <= job.requestedQuantity) {
            job.producedQuantity = updatedQuantity;

            // Update status based on progress
            if (updatedQuantity >= job.requestedQuantity) {
                job.status = 'completed';
            } else if (updatedQuantity > 0) {
                job.status = 'running';
            }

            db.updateProductionJob(job.id, job);
            productionQueue = db.getProductionQueue();
            loadProductionQueue();
            alert('تم تحديث الإنتاج بنجاح!');
        } else {
            alert('الكمية غير صحيحة!');
        }
    }
}

function cancelProduction(jobId) {
    if (confirm('هل أنت متأكد من إيقاف هذا الإنتاج؟')) {
        db.deleteProductionJob(jobId);
        productionQueue = db.getProductionQueue();
        loadProductionQueue();
        alert('تم إيقاف الإنتاج بنجاح!');
    }
}

function viewProductionHistory() {
    const completedJobs = productionQueue.filter(job => job.status === 'completed');
    if (completedJobs.length === 0) {
        alert('لا يوجد سجل إنتاج مكتمل حتى الآن.');
        return;
    }

    let historyText = 'سجل الإنتاج المكتمل:\n\n';
    completedJobs.forEach(job => {
        historyText += `${job.recipeName} - ${job.producedQuantity} كجم - ${formatStoredDate(job.startDate)}\n`;
    });

    alert(historyText);
}

function exportRecipes() {
    if (recipes.length === 0) {
        alert('لا توجد وصفات لتصديرها.');
        return;
    }

    // Create a temporary container for recipes report
    const reportContainer = document.createElement('div');
    reportContainer.style.cssText = `
        position: absolute;
        top: -10000px;
        left: -10000px;
        width: 800px;
        background: white;
        padding: 40px;
        font-family: 'Cairo', Arial, sans-serif;
        direction: rtl;
        color: #333;
        line-height: 1.6;
    `;

    const recipesContent = generateRecipesReport();

    reportContainer.innerHTML = `
        <div style="text-align: center; margin-bottom: 30px; border-bottom: 3px solid #3498db; padding-bottom: 20px;">
            <h1 style="color: #2c3e50; font-size: 28px; margin: 0;">تقرير الوصفات والخلطات</h1>
            <p style="color: #7f8c8d; margin: 10px 0 0 0;">تاريخ التقرير: ${formatArabicDate()}</p>
        </div>
        ${recipesContent}
        <div style="margin-top: 40px; text-align: center; color: #7f8c8d; font-size: 12px; border-top: 1px solid #ecf0f1; padding-top: 20px;">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة مخزون مطحنة</p>
        </div>
    `;

    document.body.appendChild(reportContainer);

    // Use html2canvas to capture the content
    html2canvas(reportContainer, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
    }).then(canvas => {
        const { jsPDF } = window.jspdf;
        const pdf = new jsPDF('p', 'mm', 'a4');

        const imgWidth = 210;
        const pageHeight = 295;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let heightLeft = imgHeight;

        let position = 0;

        pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
        }

        pdf.save('تقرير_الوصفات.pdf');
        document.body.removeChild(reportContainer);
    }).catch(error => {
        console.error('Error generating recipes PDF:', error);
        document.body.removeChild(reportContainer);
        alert('حدث خطأ في إنشاء ملف PDF للوصفات.');
    });
}

function generateRecipesReport() {
    return `
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
            <h3 style="color: #2c3e50; margin-bottom: 15px;">ملخص الوصفات</h3>
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;">
                <div style="text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #3498db;">${recipes.length}</div>
                    <div style="color: #7f8c8d;">إجمالي الوصفات</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #27ae60;">${recipes.reduce((sum, r) => sum + r.ingredients.length, 0)}</div>
                    <div style="color: #7f8c8d;">إجمالي المكونات</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #e74c3c;">${recipes.reduce((sum, r) => sum + r.totalWeight, 0)}</div>
                    <div style="color: #7f8c8d;">إجمالي الوزن (كجم)</div>
                </div>
            </div>
        </div>

        ${recipes.map((recipe, index) => `
            <div style="background: white; border: 2px solid #e9ecef; border-radius: 12px; padding: 20px; margin-bottom: 25px; page-break-inside: avoid;">
                <div style="border-bottom: 2px solid #3498db; padding-bottom: 15px; margin-bottom: 20px;">
                    <h3 style="color: #2c3e50; margin: 0; display: flex; align-items: center; gap: 10px;">
                        <span style="background: #3498db; color: white; padding: 5px 10px; border-radius: 5px; font-size: 14px;">${recipe.code}</span>
                        ${recipe.name}
                    </h3>
                    <p style="color: #7f8c8d; margin: 10px 0 0 0;">${recipe.description}</p>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <strong style="color: #2c3e50;">الوزن الإجمالي:</strong> ${recipe.totalWeight} كجم
                    </div>
                    <div>
                        <strong style="color: #2c3e50;">عدد المكونات:</strong> ${recipe.ingredients.length}
                    </div>
                    <div>
                        <strong style="color: #2c3e50;">تاريخ الإنشاء:</strong> ${formatStoredDate(recipe.createdDate)}
                    </div>
                    <div>
                        <strong style="color: #2c3e50;">أعلى نسبة:</strong> ${Math.max(...recipe.ingredients.map(i => i.percentage))}%
                    </div>
                </div>

                <h4 style="color: #2c3e50; margin-bottom: 15px;">تفاصيل المكونات:</h4>
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #34495e; color: white;">
                            <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">المكون</th>
                            <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">النسبة المئوية</th>
                            <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">الوزن (كجم)</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${recipe.ingredients.map((ingredient, idx) => `
                            <tr style="background: ${idx % 2 === 0 ? '#f8f9fa' : 'white'};">
                                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${ingredient.name}</td>
                                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${ingredient.percentage}%</td>
                                <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${ingredient.weight}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `).join('')}
    `;
}

function saveAsDraft() {
    const recipeId = parseInt(document.getElementById('selectedRecipe').value);
    const millId = parseInt(document.getElementById('selectedMill').value);
    const quantity = parseFloat(document.getElementById('productionQuantity').value);

    if (!recipeId || !millId || !quantity) {
        alert('يجب ملء البيانات الأساسية قبل حفظ المسودة');
        return;
    }

    const recipe = recipes.find(r => r.id === recipeId);
    const mill = mills.find(m => m.id === millId);

    const draft = {
        recipeId: recipeId,
        recipeName: recipe.name,
        millId: millId,
        millName: mill.name,
        requestedQuantity: quantity,
        estimatedTime: parseFloat(document.getElementById('estimatedTime').value),
        estimatedCost: parseFloat(document.getElementById('estimatedCost').value),
        priority: document.getElementById('priority').value,
        startDate: document.getElementById('startDate').value,
        notes: document.getElementById('productionNotes').value,
        isDraft: true
    };

    // Save draft to localStorage
    let drafts = JSON.parse(localStorage.getItem('productionDrafts')) || [];
    draft.id = Date.now();
    drafts.push(draft);
    localStorage.setItem('productionDrafts', JSON.stringify(drafts));

    alert('تم حفظ المسودة بنجاح!');
    closeProductionModal();
}

// Production Details and Export Functions
function viewProductionDetails(jobId) {
    const job = productionQueue.find(j => j.id === jobId);
    if (!job) {
        alert('مهمة الإنتاج غير موجودة!');
        return;
    }

    const recipe = recipes.find(r => r.id === job.recipeId);
    const mill = mills.find(m => m.id === job.millId);

    document.getElementById('productionDetailsModal').style.display = 'block';
    document.getElementById('productionDetailsTitle').textContent = `تفاصيل مهمة الإنتاج #${job.id}`;

    const progress = job.requestedQuantity > 0 ?
        Math.round((job.producedQuantity || 0) / job.requestedQuantity * 100) : 0;
    const actualTime = job.actualTime || (job.status === 'completed' ? job.estimatedTime : 0);
    const efficiency = job.estimatedTime > 0 ? Math.round((actualTime / job.estimatedTime) * 100) : 0;

    const detailsContent = `
        <div class="production-detail-section">
            <h4><i class="fas fa-info-circle"></i> معلومات أساسية</h4>
            <div class="detail-grid">
                <div class="detail-item">
                    <span class="detail-label">رقم المهمة:</span>
                    <span class="detail-value">${job.id}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">الوصفة:</span>
                    <span class="detail-value">${job.recipeName}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">كود الوصفة:</span>
                    <span class="detail-value">${job.recipeCode || '-'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">المطحنة:</span>
                    <span class="detail-value">${job.millName}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">الأولوية:</span>
                    <span class="detail-value">${job.priority || 'متوسطة'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">تاريخ البدء:</span>
                    <span class="detail-value">${formatStoredDate(job.startDate)}</span>
                </div>
            </div>
        </div>

        <div class="production-detail-section">
            <h4><i class="fas fa-chart-line"></i> تقدم الإنتاج</h4>
            <div class="progress-indicator">
                <div class="progress-label">
                    <span>نسبة الإنجاز</span>
                    <span>${progress}%</span>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar-fill ${getProgressClass(progress)}" style="width: ${progress}%">
                        <span class="progress-text">${progress}%</span>
                    </div>
                </div>
            </div>
            <div class="detail-grid">
                <div class="detail-item">
                    <span class="detail-label">الكمية المطلوبة:</span>
                    <span class="detail-value">${job.requestedQuantity} كجم</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">الكمية المنتجة:</span>
                    <span class="detail-value">${job.producedQuantity || 0} كجم</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">الكمية المتبقية:</span>
                    <span class="detail-value">${job.requestedQuantity - (job.producedQuantity || 0)} كجم</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">الحالة:</span>
                    <span class="detail-value production-status ${getProductionStatusClass(job.status)}">${getProductionStatusText(job.status)}</span>
                </div>
            </div>
        </div>

        <div class="production-detail-section">
            <h4><i class="fas fa-clock"></i> الوقت والتكلفة</h4>
            <div class="detail-grid">
                <div class="detail-item">
                    <span class="detail-label">الوقت المتوقع:</span>
                    <span class="detail-value">${job.estimatedTime || 0} ساعة</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">الوقت الفعلي:</span>
                    <span class="detail-value">${actualTime} ساعة</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">كفاءة الوقت:</span>
                    <span class="detail-value">${efficiency}%</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">التكلفة المقدرة:</span>
                    <span class="detail-value">${Math.round(job.estimatedCost || 0)} ريال</span>
                </div>
            </div>
        </div>

        ${recipe ? `
        <div class="production-detail-section">
            <h4><i class="fas fa-flask"></i> تفاصيل الوصفة</h4>
            <div class="detail-grid">
                <div class="detail-item">
                    <span class="detail-label">الوزن الأساسي:</span>
                    <span class="detail-value">${recipe.totalWeight} كجم</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">عدد المكونات:</span>
                    <span class="detail-value">${recipe.ingredients.length}</span>
                </div>
            </div>
            <h5>المكونات المطلوبة:</h5>
            <table class="production-history-table">
                <thead>
                    <tr>
                        <th>المكون</th>
                        <th>النسبة</th>
                        <th>الكمية المطلوبة</th>
                    </tr>
                </thead>
                <tbody>
                    ${job.materialRequirements ? job.materialRequirements.map(req => `
                        <tr>
                            <td>${req.name}</td>
                            <td>${req.percentage}%</td>
                            <td>${req.requiredWeight.toFixed(2)} كجم</td>
                        </tr>
                    `).join('') : '<tr><td colspan="3">لا توجد تفاصيل متاحة</td></tr>'}
                </tbody>
            </table>
        </div>
        ` : ''}

        ${job.notes ? `
        <div class="production-detail-section">
            <h4><i class="fas fa-sticky-note"></i> ملاحظات</h4>
            <p>${job.notes}</p>
        </div>
        ` : ''}
    `;

    document.getElementById('productionDetailsContent').innerHTML = detailsContent;

    // Store job ID for export
    document.getElementById('productionDetailsModal').setAttribute('data-job-id', jobId);
}

function closeProductionDetailsModal() {
    document.getElementById('productionDetailsModal').style.display = 'none';
}

function refreshProductionLog() {
    productionQueue = db.getProductionQueue();
    loadProductionLog();
    alert('تم تحديث سجل الإنتاج!');
}

function exportProductionLog() {
    if (productionQueue.length === 0) {
        alert('لا يوجد سجل إنتاج لتصديره.');
        return;
    }

    // Create a temporary container for production log report
    const reportContainer = document.createElement('div');
    reportContainer.style.cssText = `
        position: absolute;
        top: -10000px;
        left: -10000px;
        width: 900px;
        background: white;
        padding: 40px;
        font-family: 'Cairo', Arial, sans-serif;
        direction: rtl;
        color: #333;
        line-height: 1.6;
    `;

    const productionLogContent = generateProductionLogReport();

    reportContainer.innerHTML = `
        <div style="text-align: center; margin-bottom: 30px; border-bottom: 3px solid #3498db; padding-bottom: 20px;">
            <h1 style="color: #2c3e50; font-size: 28px; margin: 0;">سجل الإنتاج التفصيلي</h1>
            <p style="color: #7f8c8d; margin: 10px 0 0 0;">تاريخ التقرير: ${formatArabicDate()}</p>
        </div>
        ${productionLogContent}
        <div style="margin-top: 40px; text-align: center; color: #7f8c8d; font-size: 12px; border-top: 1px solid #ecf0f1; padding-top: 20px;">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة مخزون مطحنة</p>
        </div>
    `;

    document.body.appendChild(reportContainer);

    // Use html2canvas to capture the content
    html2canvas(reportContainer, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
    }).then(canvas => {
        const { jsPDF } = window.jspdf;
        const pdf = new jsPDF('p', 'mm', 'a4');

        const imgWidth = 210;
        const pageHeight = 295;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let heightLeft = imgHeight;

        let position = 0;

        pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
        }

        pdf.save('سجل_الإنتاج_التفصيلي.pdf');
        document.body.removeChild(reportContainer);
    }).catch(error => {
        console.error('Error generating production log PDF:', error);
        document.body.removeChild(reportContainer);
        alert('حدث خطأ في إنشاء ملف PDF لسجل الإنتاج.');
    });
}

function generateProductionLogReport() {
    const totalJobs = productionQueue.length;
    const completedJobs = productionQueue.filter(job => job.status === 'completed').length;
    const totalProduced = productionQueue.reduce((sum, job) => sum + (job.producedQuantity || 0), 0);

    return `
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
            <h3 style="color: #2c3e50; margin-bottom: 15px;">ملخص الإنتاج</h3>
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;">
                <div style="text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #3498db;">${totalJobs}</div>
                    <div style="color: #7f8c8d; font-size: 12px;">إجمالي المهام</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #27ae60;">${completedJobs}</div>
                    <div style="color: #7f8c8d; font-size: 12px;">مهام مكتملة</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #f39c12;">${totalProduced.toFixed(1)}</div>
                    <div style="color: #7f8c8d; font-size: 12px;">إجمالي الإنتاج (كجم)</div>
                </div>
            </div>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px; font-size: 11px;">
            <thead>
                <tr style="background: #34495e; color: white;">
                    <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">رقم المهمة</th>
                    <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">الوصفة</th>
                    <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">المطحنة</th>
                    <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">المطلوب</th>
                    <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">المنتج</th>
                    <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">التقدم</th>
                    <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">تاريخ البدء</th>
                    <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">الحالة</th>
                </tr>
            </thead>
            <tbody>
                ${productionQueue.map((job, index) => {
                    const progress = job.requestedQuantity > 0 ?
                        Math.round((job.producedQuantity || 0) / job.requestedQuantity * 100) : 0;
                    return `
                        <tr style="background: ${index % 2 === 0 ? '#f8f9fa' : 'white'};">
                            <td style="padding: 6px; border: 1px solid #ddd; text-align: right;">${job.id}</td>
                            <td style="padding: 6px; border: 1px solid #ddd; text-align: right;">${job.recipeName}</td>
                            <td style="padding: 6px; border: 1px solid #ddd; text-align: right;">${job.millName}</td>
                            <td style="padding: 6px; border: 1px solid #ddd; text-align: right;">${job.requestedQuantity} كجم</td>
                            <td style="padding: 6px; border: 1px solid #ddd; text-align: right;">${job.producedQuantity || 0} كجم</td>
                            <td style="padding: 6px; border: 1px solid #ddd; text-align: right;">${progress}%</td>
                            <td style="padding: 6px; border: 1px solid #ddd; text-align: right;">${formatStoredDate(job.startDate)}</td>
                            <td style="padding: 6px; border: 1px solid #ddd; text-align: right;">${getProductionStatusText(job.status)}</td>
                        </tr>
                    `;
                }).join('')}
            </tbody>
        </table>

        ${generateProductWiseReport()}
    `;
}

function generateProductWiseReport() {
    // Group by recipe
    const productGroups = {};
    productionQueue.forEach(job => {
        if (!productGroups[job.recipeName]) {
            productGroups[job.recipeName] = [];
        }
        productGroups[job.recipeName].push(job);
    });

    return `
        <h3 style="color: #2c3e50; margin-bottom: 20px; border-bottom: 2px solid #3498db; padding-bottom: 10px;">تقرير الإنتاج حسب المنتج</h3>
        ${Object.keys(productGroups).map(recipeName => {
            const jobs = productGroups[recipeName];
            const totalRequested = jobs.reduce((sum, job) => sum + job.requestedQuantity, 0);
            const totalProduced = jobs.reduce((sum, job) => sum + (job.producedQuantity || 0), 0);
            const completedJobs = jobs.filter(job => job.status === 'completed').length;

            return `
                <div style="background: white; border: 2px solid #e9ecef; border-radius: 10px; margin-bottom: 20px; padding: 15px;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">${recipeName}</h4>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; margin-bottom: 15px;">
                        <div style="text-align: center; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                            <div style="font-weight: bold; color: #3498db;">${jobs.length}</div>
                            <div style="font-size: 10px; color: #7f8c8d;">إجمالي المهام</div>
                        </div>
                        <div style="text-align: center; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                            <div style="font-weight: bold; color: #27ae60;">${completedJobs}</div>
                            <div style="font-size: 10px; color: #7f8c8d;">مهام مكتملة</div>
                        </div>
                        <div style="text-align: center; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                            <div style="font-weight: bold; color: #f39c12;">${totalRequested}</div>
                            <div style="font-size: 10px; color: #7f8c8d;">المطلوب (كجم)</div>
                        </div>
                        <div style="text-align: center; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                            <div style="font-weight: bold; color: #e74c3c;">${totalProduced.toFixed(1)}</div>
                            <div style="font-size: 10px; color: #7f8c8d;">المنتج (كجم)</div>
                        </div>
                    </div>
                </div>
            `;
        }).join('')}
    `;
}

function printProductionLog() {
    // Create a print-friendly version
    const reportContainer = document.createElement('div');
    reportContainer.innerHTML = generateProductionLogReport();

    const userData = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const currentDate = formatArabicDate();
    const currentTime = new Date().toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });

    // Create print window
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>سجل الإنتاج التفصيلي</title>
            <meta charset="UTF-8">
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body {
                    font-family: 'Cairo', Arial, sans-serif;
                    direction: rtl;
                    margin: 0;
                    padding: 20px;
                    line-height: 1.6;
                    color: #333;
                }
                .user-header {
                    background: linear-gradient(135deg, #8b5a3c, #d4a574);
                    color: white;
                    padding: 25px;
                    border-radius: 15px;
                    margin-bottom: 30px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                }
                .header-content {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                }
                .company-info {
                    display: flex;
                    align-items: center;
                    margin-bottom: 15px;
                }
                .company-info h2 {
                    margin: 0;
                    font-size: 24px;
                    font-weight: bold;
                }
                .company-info p {
                    margin: 5px 0 0 0;
                    font-size: 14px;
                    opacity: 0.9;
                }
                .user-info {
                    background: rgba(255,255,255,0.2);
                    padding: 15px;
                    border-radius: 10px;
                    min-width: 250px;
                    margin-bottom: 15px;
                }
                .user-info h3 {
                    margin: 0 0 10px 0;
                    font-size: 16px;
                    border-bottom: 1px solid rgba(255,255,255,0.3);
                    padding-bottom: 8px;
                }
                .user-details {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                    font-size: 13px;
                }
                .date-time {
                    text-align: center;
                    margin-top: 20px;
                    padding-top: 15px;
                    border-top: 1px solid rgba(255,255,255,0.3);
                }
                .date-time-content {
                    display: flex;
                    justify-content: center;
                    gap: 30px;
                    font-size: 14px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                    page-break-inside: auto;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: right;
                    page-break-inside: avoid;
                    font-size: 12px;
                }
                th {
                    background-color: #f2f2f2;
                    font-weight: bold;
                    page-break-after: avoid;
                }
                h1, h3, h4 {
                    color: #333;
                    page-break-after: avoid;
                    text-align: center;
                }
                .summary-section {
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 8px;
                    margin: 20px 0;
                    page-break-inside: avoid;
                }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                    @page { margin: 1cm; }
                }
            </style>
        </head>
        <body>
            <!-- User Header -->
            <div class="user-header">
                <div class="header-content">
                    <div class="company-info">
                        <div style="margin-left: 20px;">
                            <svg width="60" height="60" viewBox="0 0 80 80" style="filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));">
                                <g transform="translate(40, 40)">
                                    <path d="M -8 2 Q -8 1 -7 1 L 7 1 Q 8 1 8 2 L 8 9 Q 8 11 6 11 L -6 11 Q -8 11 -8 9 Z" fill="#ff9a56"/>
                                    <path d="M -6 -3 Q -6 -4 -5 -4 L 5 -4 Q 6 -4 6 -3 L 6 1 L -6 1 Z" fill="#fff"/>
                                    <circle cx="9" cy="-5" r="2.5" fill="#ff6b9d"/>
                                    <circle cx="0" cy="0" r="4" fill="#fff" opacity="0.3"/>
                                    <circle cx="0" cy="0" r="2" fill="#fff"/>
                                </g>
                            </svg>
                        </div>
                        <div>
                            <h2>مطحنة</h2>
                            <p>نظام إدارة المخزون</p>
                        </div>
                    </div>

                    <div class="user-info">
                        <div class="user-details">
                            <div><i class="fas fa-user" style="margin-left: 8px;"></i><strong>الاسم:</strong> ${userData.name || 'غير محدد'}</div>
                            <div><i class="fas fa-phone" style="margin-left: 8px;"></i><strong>الهاتف:</strong> ${userData.phone || 'غير محدد'}</div>
                        </div>
                    </div>

                    <div class="date-time">
                        <div class="date-time-content">
                            <div><i class="fas fa-calendar-alt" style="margin-left: 6px;"></i>${currentDate}</div>
                            <div><i class="fas fa-clock" style="margin-left: 6px;"></i>${currentTime}</div>
                        </div>
                    </div>
                </div>
            </div>

            <h1 style="text-align: center; border-bottom: 3px solid #8b5a3c; padding-bottom: 20px; margin-bottom: 30px;">سجل الإنتاج التفصيلي</h1>
            ${reportContainer.innerHTML}
        </body>
        </html>
    `);

    printWindow.document.close();

    // Wait for content to load then print
    setTimeout(() => {
        printWindow.print();
    }, 500);
}

function printRecipes() {
    if (recipes.length === 0) {
        alert('لا توجد وصفات للطباعة.');
        return;
    }

    const userData = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const currentDate = formatArabicDate();
    const currentTime = new Date().toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });

    // Create print window
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>تقرير الوصفات والخلطات</title>
            <meta charset="UTF-8">
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body {
                    font-family: 'Cairo', Arial, sans-serif;
                    direction: rtl;
                    margin: 0;
                    padding: 20px;
                    line-height: 1.6;
                    color: #333;
                }
                .user-header {
                    background: linear-gradient(135deg, #8b5a3c, #d4a574);
                    color: white;
                    padding: 25px;
                    border-radius: 15px;
                    margin-bottom: 30px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                }
                .header-content {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                }
                .company-info {
                    display: flex;
                    align-items: center;
                    margin-bottom: 15px;
                }
                .company-info h2 {
                    margin: 0;
                    font-size: 24px;
                    font-weight: bold;
                }
                .company-info p {
                    margin: 5px 0 0 0;
                    font-size: 14px;
                    opacity: 0.9;
                }
                .user-info {
                    background: rgba(255,255,255,0.2);
                    padding: 15px;
                    border-radius: 10px;
                    min-width: 250px;
                    margin-bottom: 15px;
                }
                .user-info h3 {
                    margin: 0 0 10px 0;
                    font-size: 16px;
                    border-bottom: 1px solid rgba(255,255,255,0.3);
                    padding-bottom: 8px;
                }
                .user-details {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                    font-size: 13px;
                }
                .date-time {
                    text-align: center;
                    margin-top: 20px;
                    padding-top: 15px;
                    border-top: 1px solid rgba(255,255,255,0.3);
                }
                .date-time-content {
                    display: flex;
                    justify-content: center;
                    gap: 30px;
                    font-size: 14px;
                }
                h1 {
                    text-align: center;
                    color: #2c3e50;
                    margin-bottom: 30px;
                    border-bottom: 3px solid #8b5a3c;
                    padding-bottom: 20px;
                }
                .recipe-card {
                    border: 2px solid #e9ecef;
                    border-radius: 10px;
                    margin-bottom: 20px;
                    padding: 15px;
                    page-break-inside: avoid;
                }
                .recipe-header {
                    background: #3498db;
                    color: white;
                    padding: 10px 15px;
                    margin: -15px -15px 15px -15px;
                    border-radius: 8px 8px 0 0;
                }
                .recipe-info {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    gap: 15px;
                    margin-bottom: 15px;
                }
                .info-item {
                    background: #f8f9fa;
                    padding: 10px;
                    border-radius: 5px;
                }
                .ingredients-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 10px;
                }
                .ingredients-table th,
                .ingredients-table td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: right;
                }
                .ingredients-table th {
                    background: #34495e;
                    color: white;
                    font-weight: bold;
                }
                @media print {
                    body { margin: 0; }
                    @page { margin: 1cm; }
                }
            </style>
        </head>
        <body>
            <!-- User Header -->
            <div class="user-header">
                <div class="header-content">
                    <div class="company-info">
                        <div style="margin-left: 20px;">
                            <svg width="60" height="60" viewBox="0 0 80 80" style="filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));">
                                <g transform="translate(40, 40)">
                                    <path d="M -8 2 Q -8 1 -7 1 L 7 1 Q 8 1 8 2 L 8 9 Q 8 11 6 11 L -6 11 Q -8 11 -8 9 Z" fill="#ff9a56"/>
                                    <path d="M -6 -3 Q -6 -4 -5 -4 L 5 -4 Q 6 -4 6 -3 L 6 1 L -6 1 Z" fill="#fff"/>
                                    <circle cx="9" cy="-5" r="2.5" fill="#ff6b9d"/>
                                    <circle cx="0" cy="0" r="4" fill="#fff" opacity="0.3"/>
                                    <circle cx="0" cy="0" r="2" fill="#fff"/>
                                </g>
                            </svg>
                        </div>
                        <div>
                            <h2>مطحنة</h2>
                            <p>نظام إدارة المخزون</p>
                        </div>
                    </div>

                    <div class="user-info">
                        <div class="user-details">
                            <div><i class="fas fa-user" style="margin-left: 8px;"></i><strong>الاسم:</strong> ${userData.name || 'غير محدد'}</div>
                            <div><i class="fas fa-phone" style="margin-left: 8px;"></i><strong>الهاتف:</strong> ${userData.phone || 'غير محدد'}</div>
                        </div>
                    </div>

                    <div class="date-time">
                        <div class="date-time-content">
                            <div><i class="fas fa-calendar-alt" style="margin-left: 6px;"></i>${currentDate}</div>
                            <div><i class="fas fa-clock" style="margin-left: 6px;"></i>${currentTime}</div>
                        </div>
                    </div>
                </div>
            </div>

            <h1>تقرير الوصفات والخلطات</h1>

            ${recipes.map(recipe => `
                <div class="recipe-card">
                    <div class="recipe-header">
                        <h3 style="margin: 0;">${recipe.name} (${recipe.code})</h3>
                    </div>

                    <div class="recipe-info">
                        <div class="info-item">
                            <strong>الوزن الأساسي:</strong> ${recipe.totalWeight} كجم
                        </div>
                        <div class="info-item">
                            <strong>تاريخ الإنشاء:</strong> ${formatStoredDate(recipe.createdDate)}
                        </div>
                        <div class="info-item">
                            <strong>عدد المكونات:</strong> ${recipe.ingredients.length}
                        </div>
                        <div class="info-item">
                            <strong>الوصف:</strong> ${recipe.description || 'لا يوجد وصف'}
                        </div>
                    </div>

                    <h4>المكونات:</h4>
                    <table class="ingredients-table">
                        <thead>
                            <tr>
                                <th>المكون</th>
                                <th>النسبة المئوية</th>
                                <th>الوزن (كجم)</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${recipe.ingredients.map(ingredient => `
                                <tr>
                                    <td>${ingredient.name}</td>
                                    <td>${ingredient.percentage}%</td>
                                    <td>${ingredient.weight}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `).join('')}
        </body>
        </html>
    `);

    printWindow.document.close();

    // Wait for content to load then print
    setTimeout(() => {
        printWindow.print();
    }, 500);
}

function printJobDetails() {
    const jobId = document.getElementById('productionDetailsModal').getAttribute('data-job-id');
    if (!jobId) {
        alert('لا يمكن العثور على معرف المهمة!');
        return;
    }

    const job = productionQueue.find(j => j.id === parseInt(jobId));
    if (!job) {
        alert('مهمة الإنتاج غير موجودة!');
        return;
    }

    const recipe = recipes.find(r => r.id === job.recipeId);
    const mill = mills.find(m => m.id === job.millId);

    const userData = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const currentDate = formatArabicDate();
    const currentTime = new Date().toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });

    // Create print window
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>تفاصيل مهمة الإنتاج #${job.id}</title>
            <meta charset="UTF-8">
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body {
                    font-family: 'Cairo', Arial, sans-serif;
                    direction: rtl;
                    margin: 0;
                    padding: 20px;
                    line-height: 1.6;
                    color: #333;
                }
                .user-header {
                    background: linear-gradient(135deg, #8b5a3c, #d4a574);
                    color: white;
                    padding: 25px;
                    border-radius: 15px;
                    margin-bottom: 30px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                }
                .header-content {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                }
                .company-info {
                    display: flex;
                    align-items: center;
                    margin-bottom: 15px;
                }
                .company-info h2 {
                    margin: 0;
                    font-size: 24px;
                    font-weight: bold;
                }
                .company-info p {
                    margin: 5px 0 0 0;
                    font-size: 14px;
                    opacity: 0.9;
                }
                .user-info {
                    background: rgba(255,255,255,0.2);
                    padding: 15px;
                    border-radius: 10px;
                    min-width: 250px;
                    margin-bottom: 15px;
                }
                .user-info h3 {
                    margin: 0 0 10px 0;
                    font-size: 16px;
                    border-bottom: 1px solid rgba(255,255,255,0.3);
                    padding-bottom: 8px;
                }
                .user-details {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                    font-size: 13px;
                }
                .date-time {
                    text-align: center;
                    margin-top: 20px;
                    padding-top: 15px;
                    border-top: 1px solid rgba(255,255,255,0.3);
                }
                .date-time-content {
                    display: flex;
                    justify-content: center;
                    gap: 30px;
                    font-size: 14px;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 3px solid #8b5a3c;
                    padding-bottom: 20px;
                }
                h1 {
                    color: #2c3e50;
                    font-size: 28px;
                    margin: 0 0 10px 0;
                }
                .section {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                    margin-bottom: 25px;
                    border-right: 4px solid #3498db;
                    page-break-inside: avoid;
                }
                .section h3 {
                    color: #2c3e50;
                    margin-bottom: 15px;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }
                .info-grid {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    gap: 15px;
                }
                .info-item {
                    background: white;
                    padding: 15px;
                    border-radius: 8px;
                }
                .info-label {
                    font-weight: bold;
                    color: #7f8c8d;
                    display: block;
                    margin-bottom: 5px;
                }
                .info-value {
                    color: #2c3e50;
                    font-size: 1.1rem;
                }
                .progress-section {
                    margin: 20px 0;
                }
                .progress-bar {
                    width: 100%;
                    height: 25px;
                    background: #e9ecef;
                    border-radius: 12px;
                    overflow: hidden;
                    position: relative;
                }
                .progress-fill {
                    height: 100%;
                    background: linear-gradient(90deg, #27ae60, #2ecc71);
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: bold;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 15px;
                    background: white;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 10px;
                    text-align: right;
                }
                th {
                    background: #34495e;
                    color: white;
                    font-weight: bold;
                }
                tbody tr:nth-child(even) {
                    background: #f8f9fa;
                }
                .footer {
                    margin-top: 40px;
                    text-align: center;
                    color: #7f8c8d;
                    font-size: 12px;
                    border-top: 1px solid #ecf0f1;
                    padding-top: 20px;
                }
                @media print {
                    body { margin: 0; }
                    @page {
                        margin: 1cm;
                        size: A4;
                    }
                }
            </style>
        </head>
        <body>
            <!-- User Header -->
            <div class="user-header">
                <div class="header-content">
                    <div class="company-info">
                        <div style="margin-left: 20px;">
                            <svg width="60" height="60" viewBox="0 0 80 80" style="filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));">
                                <g transform="translate(40, 40)">
                                    <path d="M -8 2 Q -8 1 -7 1 L 7 1 Q 8 1 8 2 L 8 9 Q 8 11 6 11 L -6 11 Q -8 11 -8 9 Z" fill="#ff9a56"/>
                                    <path d="M -6 -3 Q -6 -4 -5 -4 L 5 -4 Q 6 -4 6 -3 L 6 1 L -6 1 Z" fill="#fff"/>
                                    <circle cx="9" cy="-5" r="2.5" fill="#ff6b9d"/>
                                    <circle cx="0" cy="0" r="4" fill="#fff" opacity="0.3"/>
                                    <circle cx="0" cy="0" r="2" fill="#fff"/>
                                </g>
                            </svg>
                        </div>
                        <div>
                            <h2>مطحنة</h2>
                            <p>نظام إدارة المخزون</p>
                        </div>
                    </div>

                    <div class="user-info">
                        <div class="user-details">
                            <div><i class="fas fa-user" style="margin-left: 8px;"></i><strong>الاسم:</strong> ${userData.name || 'غير محدد'}</div>
                            <div><i class="fas fa-phone" style="margin-left: 8px;"></i><strong>الهاتف:</strong> ${userData.phone || 'غير محدد'}</div>
                        </div>
                    </div>

                    <div class="date-time">
                        <div class="date-time-content">
                            <div><i class="fas fa-calendar-alt" style="margin-left: 6px;"></i>${currentDate}</div>
                            <div><i class="fas fa-clock" style="margin-left: 6px;"></i>${currentTime}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="header">
                <h1>تفاصيل مهمة الإنتاج #${job.id}</h1>
            </div>

            ${generateJobDetailsForPrint(job, recipe, mill)}

            <div class="footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة مخزون مطحنة</p>
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();

    // Wait for content to load then print
    setTimeout(() => {
        printWindow.print();
    }, 1000);
}

function generateJobDetailsForPrint(job, recipe, mill) {
    const progress = job.requestedQuantity > 0 ?
        Math.round((job.producedQuantity || 0) / job.requestedQuantity * 100) : 0;
    const actualTime = job.actualTime || (job.status === 'completed' ? job.estimatedTime : 0);
    const efficiency = job.estimatedTime > 0 ? Math.round((actualTime / job.estimatedTime) * 100) : 0;
    const endDate = job.endDate || (job.status === 'completed' ? job.startDate : 'لم ينته بعد');

    return `
        <!-- معلومات أساسية -->
        <div class="section">
            <h3>📋 معلومات أساسية</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">رقم المهمة:</span>
                    <span class="info-value">${job.id}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الوصفة:</span>
                    <span class="info-value">${job.recipeName}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">كود الوصفة:</span>
                    <span class="info-value">${job.recipeCode || 'غير محدد'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">المطحنة:</span>
                    <span class="info-value">${job.millName}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الأولوية:</span>
                    <span class="info-value">${job.priority || 'متوسطة'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ البدء:</span>
                    <span class="info-value">${formatStoredDate(job.startDate)}</span>
                </div>
            </div>
        </div>

        <!-- تقدم الإنتاج -->
        <div class="section">
            <h3>📊 تقدم الإنتاج</h3>
            <div class="progress-section">
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <span style="font-weight: bold;">نسبة الإنجاز</span>
                    <span style="font-weight: bold; color: #27ae60; font-size: 1.2rem;">${progress}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${progress}%">${progress}%</div>
                </div>
            </div>

            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">الكمية المطلوبة:</span>
                    <span class="info-value">${job.requestedQuantity} كجم</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الكمية المنتجة:</span>
                    <span class="info-value">${job.producedQuantity || 0} كجم</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الكمية المتبقية:</span>
                    <span class="info-value">${job.requestedQuantity - (job.producedQuantity || 0)} كجم</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الحالة:</span>
                    <span class="info-value">${getProductionStatusText(job.status)}</span>
                </div>
            </div>
        </div>

        <!-- الوقت والتكلفة -->
        <div class="section">
            <h3>⏰ الوقت والتكلفة</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">الوقت المتوقع:</span>
                    <span class="info-value">${job.estimatedTime || 0} ساعة</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الوقت الفعلي:</span>
                    <span class="info-value">${actualTime} ساعة</span>
                </div>
                <div class="info-item">
                    <span class="info-label">كفاءة الوقت:</span>
                    <span class="info-value">${efficiency}%</span>
                </div>
                <div class="info-item">
                    <span class="info-label">التكلفة المقدرة:</span>
                    <span class="info-value">${Math.round(job.estimatedCost || 0)} ريال</span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ الانتهاء:</span>
                    <span class="info-value">${endDate === 'لم ينته بعد' ? endDate : formatStoredDate(endDate)}</span>
                </div>
            </div>
        </div>

        ${recipe ? `
        <!-- تفاصيل الوصفة -->
        <div class="section">
            <h3>🧪 تفاصيل الوصفة</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">الوزن الأساسي:</span>
                    <span class="info-value">${recipe.totalWeight} كجم</span>
                </div>
                <div class="info-item">
                    <span class="info-label">عدد المكونات:</span>
                    <span class="info-value">${recipe.ingredients.length}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ إنشاء الوصفة:</span>
                    <span class="info-value">${formatStoredDate(recipe.createdDate)}</span>
                </div>
            </div>

            <h4>المكونات المطلوبة:</h4>
            <table>
                <thead>
                    <tr>
                        <th>المكون</th>
                        <th>النسبة المئوية</th>
                        <th>الكمية المطلوبة (كجم)</th>
                    </tr>
                </thead>
                <tbody>
                    ${job.materialRequirements ? job.materialRequirements.map(req => `
                        <tr>
                            <td>${req.name}</td>
                            <td>${req.percentage}%</td>
                            <td>${req.requiredWeight.toFixed(2)}</td>
                        </tr>
                    `).join('') : '<tr><td colspan="3" style="text-align: center; color: #7f8c8d;">لا توجد تفاصيل متاحة</td></tr>'}
                </tbody>
            </table>
        </div>
        ` : ''}

        ${mill ? `
        <!-- تفاصيل المطحنة -->
        <div class="section">
            <h3>⚙️ تفاصيل المطحنة المستخدمة</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">نوع المطحنة:</span>
                    <span class="info-value">${mill.type}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">السعة:</span>
                    <span class="info-value">${mill.capacity} كجم/ساعة</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الكفاءة:</span>
                    <span class="info-value">${mill.efficiency}%</span>
                </div>
                <div class="info-item">
                    <span class="info-label">ساعات التشغيل اليومية:</span>
                    <span class="info-value">${mill.workingHours || 0} ساعة</span>
                </div>
                <div class="info-item">
                    <span class="info-label">تكلفة التشغيل:</span>
                    <span class="info-value">${mill.operatingCost || 0} ريال/ساعة</span>
                </div>
                <div class="info-item">
                    <span class="info-label">حالة المطحنة:</span>
                    <span class="info-value">${mill.status}</span>
                </div>
            </div>
        </div>
        ` : ''}

        ${job.notes ? `
        <!-- الملاحظات -->
        <div class="section">
            <h3>📝 ملاحظات خاصة</h3>
            <div style="background: white; padding: 20px; border-radius: 8px; border-right: 3px solid #17a2b8;">
                <p style="margin: 0; line-height: 1.8; font-size: 1.1rem;">${job.notes}</p>
            </div>
        </div>
        ` : ''}
    `;
}

function exportJobDetails() {
    const jobId = document.getElementById('productionDetailsModal').getAttribute('data-job-id');
    if (!jobId) {
        alert('لا يمكن العثور على معرف المهمة!');
        return;
    }

    const job = productionQueue.find(j => j.id === parseInt(jobId));
    if (!job) {
        alert('مهمة الإنتاج غير موجودة!');
        return;
    }

    const recipe = recipes.find(r => r.id === job.recipeId);
    const mill = mills.find(m => m.id === job.millId);

    // Create a temporary container for job details report
    const reportContainer = document.createElement('div');
    reportContainer.style.cssText = `
        position: absolute;
        top: -10000px;
        left: -10000px;
        width: 800px;
        background: white;
        padding: 40px;
        font-family: 'Cairo', Arial, sans-serif;
        direction: rtl;
        color: #333;
        line-height: 1.6;
    `;

    const jobDetailsContent = generateJobDetailsReport(job, recipe, mill);

    reportContainer.innerHTML = `
        <div style="text-align: center; margin-bottom: 30px; border-bottom: 3px solid #3498db; padding-bottom: 20px;">
            <h1 style="color: #2c3e50; font-size: 28px; margin: 0;">تفاصيل مهمة الإنتاج #${job.id}</h1>
            <p style="color: #7f8c8d; margin: 10px 0 0 0;">تاريخ التقرير: ${formatArabicDate()}</p>
        </div>
        ${jobDetailsContent}
        <div style="margin-top: 40px; text-align: center; color: #7f8c8d; font-size: 12px; border-top: 1px solid #ecf0f1; padding-top: 20px;">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة مخزون مطحنة</p>
        </div>
    `;

    document.body.appendChild(reportContainer);

    // Use html2canvas to capture the content
    html2canvas(reportContainer, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
    }).then(canvas => {
        const { jsPDF } = window.jspdf;
        const pdf = new jsPDF('p', 'mm', 'a4');

        const imgWidth = 210;
        const pageHeight = 295;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let heightLeft = imgHeight;

        let position = 0;

        pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
        }

        pdf.save(`تفاصيل_مهمة_الإنتاج_${job.id}.pdf`);
        document.body.removeChild(reportContainer);
        alert('تم تصدير تفاصيل المهمة بنجاح!');
    }).catch(error => {
        console.error('Error generating job details PDF:', error);
        document.body.removeChild(reportContainer);
        alert('حدث خطأ في إنشاء ملف PDF لتفاصيل المهمة.');
    });
}

function generateJobDetailsReport(job, recipe, mill) {
    const progress = job.requestedQuantity > 0 ?
        Math.round((job.producedQuantity || 0) / job.requestedQuantity * 100) : 0;
    const actualTime = job.actualTime || (job.status === 'completed' ? job.estimatedTime : 0);
    const efficiency = job.estimatedTime > 0 ? Math.round((actualTime / job.estimatedTime) * 100) : 0;
    const endDate = job.endDate || (job.status === 'completed' ? job.startDate : 'لم ينته بعد');

    return `
        <!-- معلومات أساسية -->
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 4px solid #3498db;">
            <h3 style="color: #2c3e50; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-info-circle" style="color: #3498db;"></i>
                معلومات أساسية
            </h3>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">رقم المهمة:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.2rem; font-weight: bold;">${job.id}</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">الوصفة:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.1rem;">${job.recipeName}</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">كود الوصفة:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.1rem;">${job.recipeCode || 'غير محدد'}</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">المطحنة:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.1rem;">${job.millName}</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">الأولوية:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.1rem;">${job.priority || 'متوسطة'}</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">تاريخ البدء:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.1rem;">${formatStoredDate(job.startDate)}</span>
                </div>
            </div>
        </div>

        <!-- تقدم الإنتاج -->
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 4px solid #27ae60;">
            <h3 style="color: #2c3e50; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-chart-line" style="color: #27ae60;"></i>
                تقدم الإنتاج
            </h3>

            <!-- شريط التقدم -->
            <div style="margin-bottom: 20px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <span style="font-weight: bold; color: #2c3e50;">نسبة الإنجاز</span>
                    <span style="font-weight: bold; color: #27ae60; font-size: 1.2rem;">${progress}%</span>
                </div>
                <div style="width: 100%; height: 25px; background: #e9ecef; border-radius: 12px; overflow: hidden;">
                    <div style="height: 100%; background: linear-gradient(90deg, #27ae60, #2ecc71); width: ${progress}%; border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                        <span style="color: white; font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">${progress}%</span>
                    </div>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">الكمية المطلوبة:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.2rem; font-weight: bold;">${job.requestedQuantity} كجم</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">الكمية المنتجة:</strong><br>
                    <span style="color: #27ae60; font-size: 1.2rem; font-weight: bold;">${job.producedQuantity || 0} كجم</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">الكمية المتبقية:</strong><br>
                    <span style="color: #e74c3c; font-size: 1.2rem; font-weight: bold;">${job.requestedQuantity - (job.producedQuantity || 0)} كجم</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">الحالة:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.1rem; font-weight: bold;">${getProductionStatusText(job.status)}</span>
                </div>
            </div>
        </div>

        <!-- الوقت والتكلفة -->
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 4px solid #f39c12;">
            <h3 style="color: #2c3e50; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-clock" style="color: #f39c12;"></i>
                الوقت والتكلفة
            </h3>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">الوقت المتوقع:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.2rem; font-weight: bold;">${job.estimatedTime || 0} ساعة</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">الوقت الفعلي:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.2rem; font-weight: bold;">${actualTime} ساعة</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">كفاءة الوقت:</strong><br>
                    <span style="color: ${efficiency <= 100 ? '#27ae60' : '#e74c3c'}; font-size: 1.2rem; font-weight: bold;">${efficiency}%</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">التكلفة المقدرة:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.2rem; font-weight: bold;">${Math.round(job.estimatedCost || 0)} ريال</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">تاريخ الانتهاء:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.1rem;">${endDate === 'لم ينته بعد' ? endDate : formatStoredDate(endDate)}</span>
                </div>
            </div>
        </div>

        ${recipe ? `
        <!-- تفاصيل الوصفة -->
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 4px solid #9b59b6;">
            <h3 style="color: #2c3e50; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-flask" style="color: #9b59b6;"></i>
                تفاصيل الوصفة
            </h3>
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-bottom: 20px;">
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">الوزن الأساسي:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.2rem; font-weight: bold;">${recipe.totalWeight} كجم</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">عدد المكونات:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.2rem; font-weight: bold;">${recipe.ingredients.length}</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">تاريخ إنشاء الوصفة:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.1rem;">${formatStoredDate(recipe.createdDate)}</span>
                </div>
            </div>

            <h4 style="color: #2c3e50; margin-bottom: 15px;">المكونات المطلوبة:</h4>
            <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden;">
                <thead>
                    <tr style="background: #9b59b6; color: white;">
                        <th style="padding: 12px; text-align: right; border-bottom: 2px solid #8e44ad;">المكون</th>
                        <th style="padding: 12px; text-align: right; border-bottom: 2px solid #8e44ad;">النسبة المئوية</th>
                        <th style="padding: 12px; text-align: right; border-bottom: 2px solid #8e44ad;">الكمية المطلوبة (كجم)</th>
                    </tr>
                </thead>
                <tbody>
                    ${job.materialRequirements ? job.materialRequirements.map((req, index) => `
                        <tr style="background: ${index % 2 === 0 ? '#f8f9fa' : 'white'};">
                            <td style="padding: 10px; border-bottom: 1px solid #ecf0f1; text-align: right;">${req.name}</td>
                            <td style="padding: 10px; border-bottom: 1px solid #ecf0f1; text-align: right;">${req.percentage}%</td>
                            <td style="padding: 10px; border-bottom: 1px solid #ecf0f1; text-align: right;">${req.requiredWeight.toFixed(2)}</td>
                        </tr>
                    `).join('') : '<tr><td colspan="3" style="padding: 20px; text-align: center; color: #7f8c8d;">لا توجد تفاصيل متاحة</td></tr>'}
                </tbody>
            </table>
        </div>
        ` : ''}

        ${mill ? `
        <!-- تفاصيل المطحنة -->
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 4px solid #34495e;">
            <h3 style="color: #2c3e50; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-cogs" style="color: #34495e;"></i>
                تفاصيل المطحنة المستخدمة
            </h3>
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">نوع المطحنة:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.1rem;">${mill.type}</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">السعة:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.1rem;">${mill.capacity} كجم/ساعة</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">الكفاءة:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.1rem;">${mill.efficiency}%</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">ساعات التشغيل اليومية:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.1rem;">${mill.workingHours || 0} ساعة</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">تكلفة التشغيل:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.1rem;">${mill.operatingCost || 0} ريال/ساعة</span>
                </div>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <strong style="color: #7f8c8d;">حالة المطحنة:</strong><br>
                    <span style="color: #2c3e50; font-size: 1.1rem;">${mill.status}</span>
                </div>
            </div>
        </div>
        ` : ''}

        ${job.notes ? `
        <!-- الملاحظات -->
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px; border-right: 4px solid #17a2b8;">
            <h3 style="color: #2c3e50; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-sticky-note" style="color: #17a2b8;"></i>
                ملاحظات خاصة
            </h3>
            <div style="background: white; padding: 20px; border-radius: 8px; border-right: 3px solid #17a2b8;">
                <p style="color: #2c3e50; line-height: 1.8; margin: 0; font-size: 1.1rem;">${job.notes}</p>
            </div>
        </div>
        ` : ''}
    `;
}

// Modal functionality
function openAddModal(type) {
    const modal = document.getElementById('addModal');
    const modalTitle = document.getElementById('modalTitle');
    const formFields = document.getElementById('formFields');
    
    let title = '';
    let fields = '';
    
    switch(type) {
        case 'raw-material':
            title = 'إضافة مادة خام جديدة';
            fields = `
                <div class="form-row">
                    <div class="form-group">
                        <label for="code">رقم الكود</label>
                        <input type="text" id="code" name="code" required>
                    </div>
                    <div class="form-group">
                        <label for="name">اسم المادة</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="weight">الوزن (كجم)</label>
                        <input type="number" id="weight" name="weight" required>
                    </div>
                    <div class="form-group">
                        <label for="quantity">الكمية</label>
                        <input type="number" id="quantity" name="quantity" required>
                    </div>
                </div>
            `;
            break;
        case 'finished-product':
            title = 'إضافة مادة جاهزة جديدة';
            fields = `
                <div class="form-row">
                    <div class="form-group">
                        <label for="code">رقم الكود</label>
                        <input type="text" id="code" name="code" required>
                    </div>
                    <div class="form-group">
                        <label for="name">اسم المنتج</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="weight">الوزن (كجم)</label>
                        <input type="number" id="weight" name="weight" required>
                    </div>
                    <div class="form-group">
                        <label for="quantity">الكمية المتاحة</label>
                        <input type="number" id="quantity" name="quantity" required>
                    </div>
                </div>
            `;
            break;
        case 'mixture':
            title = 'إضافة خلطة جديدة';
            fields = `
                <div class="form-row">
                    <div class="form-group">
                        <label for="code">كود التشغيل</label>
                        <input type="text" id="code" name="code" required>
                    </div>
                    <div class="form-group">
                        <label for="name">اسم الخلطة</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="components">المكونات</label>
                        <textarea id="components" name="components" rows="3" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="weight">الوزن الإجمالي (كجم)</label>
                        <input type="number" id="weight" name="weight" required>
                    </div>
                </div>
            `;
            break;
        case 'mill':
            title = 'إضافة مطحنة جديدة';
            fields = `
                <div class="form-row">
                    <div class="form-group">
                        <label for="code">رقم المطحنة</label>
                        <input type="text" id="code" name="code" required>
                    </div>
                    <div class="form-group">
                        <label for="name">اسم المطحنة</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="type">النوع</label>
                        <select id="type" name="type" required>
                            <option value="">اختر النوع</option>
                            <option value="مطحنة ثقيلة">مطحنة ثقيلة</option>
                            <option value="مطحنة متوسطة">مطحنة متوسطة</option>
                            <option value="مطحنة خفيفة">مطحنة خفيفة</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="capacity">السعة (كجم/ساعة)</label>
                        <input type="number" id="capacity" name="capacity" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="efficiency">نسبة التشغيل (%)</label>
                        <input type="number" id="efficiency" name="efficiency" min="0" max="100" required>
                    </div>
                    <div class="form-group">
                        <label for="workingHours">ساعات التشغيل اليومية</label>
                        <input type="number" id="workingHours" name="workingHours" min="0" max="24" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="operatingCost">تكلفة التشغيل (ريال/ساعة)</label>
                        <input type="number" id="operatingCost" name="operatingCost" min="0" required>
                    </div>
                    <div class="form-group">
                        <label for="status">حالة التشغيل</label>
                        <select id="status" name="status" required>
                            <option value="">اختر الحالة</option>
                            <option value="نشطة">نشطة</option>
                            <option value="متوقفة">متوقفة</option>
                            <option value="صيانة">صيانة</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="lastMaintenance">آخر صيانة</label>
                        <input type="date" id="lastMaintenance" name="lastMaintenance" required>
                    </div>
                    <div class="form-group">
                        <label for="nextMaintenance">الصيانة القادمة</label>
                        <input type="date" id="nextMaintenance" name="nextMaintenance">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="maintenanceType">نوع الصيانة</label>
                        <select id="maintenanceType" name="maintenanceType">
                            <option value="">اختر نوع الصيانة</option>
                            <option value="صيانة دورية">صيانة دورية</option>
                            <option value="فحص شامل">فحص شامل</option>
                            <option value="تغيير قطع">تغيير قطع</option>
                            <option value="إصلاح عطل">إصلاح عطل</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="priority">الأولوية</label>
                        <select id="priority" name="priority">
                            <option value="">اختر الأولوية</option>
                            <option value="عالية">عالية</option>
                            <option value="متوسطة">متوسطة</option>
                            <option value="منخفضة">منخفضة</option>
                        </select>
                    </div>
                </div>
            `;
            break;
    }
    
    modalTitle.textContent = title;
    formFields.innerHTML = fields;
    modal.style.display = 'block';
    
    // Store the type for form submission
    document.getElementById('addForm').setAttribute('data-type', type);
}

function closeModal() {
    document.getElementById('addModal').style.display = 'none';
    document.getElementById('addForm').reset();
}

// Form submission
function setupFormHandlers() {
    // User form
    document.querySelector('.user-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        const userInfo = {
            name: formData.get('userName'),
            email: formData.get('userEmail'),
            phone: formData.get('userPhone'),
            role: formData.get('userRole'),
            address: formData.get('userAddress')
        };
        db.saveUserInfo(userInfo);
        alert('تم حفظ بيانات المستخدم بنجاح!');
    });

    // Add form
    document.getElementById('addForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const type = this.getAttribute('data-type');
        const formData = new FormData(this);

        const newItem = {
            id: Date.now(),
            code: formData.get('code'),
            name: formData.get('name'),
            weight: parseFloat(formData.get('weight')),
            quantity: parseInt(formData.get('quantity')),
            date: new Date().toISOString().split('T')[0]
        };

        if (type === 'mixture') {
            newItem.components = formData.get('components');
        }

        if (type === 'mill') {
            newItem.type = formData.get('type');
            newItem.capacity = parseInt(formData.get('capacity'));
            newItem.efficiency = parseInt(formData.get('efficiency'));
            newItem.workingHours = parseInt(formData.get('workingHours'));
            newItem.operatingCost = parseFloat(formData.get('operatingCost'));
            newItem.status = formData.get('status');
            newItem.lastMaintenance = formData.get('lastMaintenance');
            newItem.nextMaintenance = formData.get('nextMaintenance');
            newItem.maintenanceType = formData.get('maintenanceType');
            newItem.priority = formData.get('priority');

            // Calculate daily production based on capacity, working hours, and efficiency
            const workingHours = parseInt(formData.get('workingHours')) || 0;
            const capacity = parseInt(formData.get('capacity')) || 0;
            const efficiency = parseInt(formData.get('efficiency')) || 0;
            newItem.dailyProduction = Math.round((capacity * workingHours * efficiency) / 100);
        }

        switch(type) {
            case 'raw-material':
                db.addRawMaterial(newItem);
                rawMaterials = db.getRawMaterials();
                loadRawMaterials();
                break;
            case 'finished-product':
                db.addFinishedProduct(newItem);
                finishedProducts = db.getFinishedProducts();
                loadFinishedProducts();
                break;
            case 'mixture':
                db.addMixture(newItem);
                mixtures = db.getMixtures();
                loadMixtures();
                break;
            case 'mill':
                db.addMill(newItem);
                mills = db.getMills();
                loadMills();
                break;
        }

        closeModal();
        alert('تم إضافة العنصر بنجاح!');
    });

    // Recipe form
    document.getElementById('recipeForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        const editId = this.getAttribute('data-edit-id');

        // Collect ingredients
        const ingredients = [];
        const ingredientRows = document.querySelectorAll('.ingredient-row');
        let totalPercentage = 0;

        ingredientRows.forEach((row, index) => {
            const ingredientSelect = row.querySelector(`select[name="ingredient_${index}"]`);
            const percentageInput = row.querySelector(`input[name="percentage_${index}"]`);
            const weightInput = row.querySelector(`input[name="weight_${index}"]`);

            if (ingredientSelect && percentageInput && ingredientSelect.value && percentageInput.value) {
                const percentage = parseFloat(percentageInput.value);
                totalPercentage += percentage;

                ingredients.push({
                    name: ingredientSelect.value,
                    percentage: percentage,
                    weight: parseFloat(weightInput.value) || 0
                });
            }
        });

        // Validate total percentage
        if (Math.abs(totalPercentage - 100) > 0.1) {
            alert('مجموع النسب المئوية يجب أن يساوي 100%');
            return;
        }

        if (ingredients.length === 0) {
            alert('يجب إضافة مكون واحد على الأقل');
            return;
        }

        const recipeData = {
            code: formData.get('recipeCode'),
            name: formData.get('recipeName'),
            description: formData.get('recipeDescription') || '',
            totalWeight: parseFloat(formData.get('totalWeight')),
            ingredients: ingredients
        };

        if (editId) {
            // Update existing recipe
            db.updateRecipe(parseInt(editId), recipeData);
            alert('تم تعديل الوصفة بنجاح!');
        } else {
            // Add new recipe
            db.addRecipe(recipeData);
            alert('تم حفظ الوصفة بنجاح!');
        }

        recipes = db.getRecipes();
        loadRecipes();
        closeRecipeModal();
    });

    // Production form
    document.getElementById('productionForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        const editId = this.getAttribute('data-edit-id');

        const recipeId = parseInt(formData.get('selectedRecipe'));
        const millId = parseInt(formData.get('selectedMill'));
        const recipe = recipes.find(r => r.id === recipeId);
        const mill = mills.find(m => m.id === millId);

        if (!recipe || !mill) {
            alert('يجب اختيار الوصفة والمطحنة');
            return;
        }

        if (editId) {
            // Update existing job
            const jobIndex = productionQueue.findIndex(j => j.id === parseInt(editId));

            if (jobIndex !== -1) {
                productionQueue[jobIndex].requestedQuantity = parseFloat(formData.get('productionQuantity'));
                productionQueue[jobIndex].estimatedTime = parseFloat(formData.get('estimatedTime'));
                productionQueue[jobIndex].estimatedCost = parseFloat(formData.get('estimatedCost'));
                productionQueue[jobIndex].priority = formData.get('priority');
                productionQueue[jobIndex].startDate = formData.get('startDate');
                productionQueue[jobIndex].notes = formData.get('productionNotes');

                db.updateProductionQueue(productionQueue);
                loadProductionQueue();
                loadProductionQueueInNewPage();
                loadProductionStatistics();
                closeProductionModal();
                alert('تم تعديل المهمة بنجاح!');
                return;
            }
        }

        // Check material availability
        const quantity = parseFloat(formData.get('productionQuantity'));
        const multiplier = quantity / recipe.totalWeight;
        let canProduce = true;
        let missingMaterials = [];

        recipe.ingredients.forEach(ingredient => {
            const requiredWeight = ingredient.weight * multiplier;
            const availability = checkMaterialAvailability(ingredient.name, requiredWeight);
            if (availability.class === 'availability-unavailable') {
                canProduce = false;
                missingMaterials.push(ingredient.name);
            }
        });

        if (!canProduce) {
            const confirmProduce = confirm(
                `تحذير: المواد التالية غير متوفرة بالكمية المطلوبة:\n${missingMaterials.join(', ')}\n\nهل تريد المتابعة رغم ذلك؟`
            );
            if (!confirmProduce) {
                return;
            }
        }

        const newJob = {
            recipeId: recipeId,
            recipeName: recipe.name,
            recipeCode: recipe.code,
            millId: millId,
            millName: mill.name,
            requestedQuantity: quantity,
            estimatedTime: parseFloat(formData.get('estimatedTime')),
            estimatedCost: parseFloat(formData.get('estimatedCost')),
            priority: formData.get('priority'),
            startDate: formData.get('startDate'),
            notes: formData.get('productionNotes'),
            materialRequirements: recipe.ingredients.map(ingredient => ({
                name: ingredient.name,
                requiredWeight: ingredient.weight * multiplier,
                percentage: ingredient.percentage
            }))
        };

        db.addProductionJob(newJob);
        productionQueue = db.getProductionQueue();
        loadProductionQueue();
        loadProductionQueueInNewPage();
        loadProductionStatistics();
        closeProductionModal();
        alert('تم إضافة مهمة الإنتاج بنجاح!');
    });

    // Edit form
    document.getElementById('editForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const type = this.getAttribute('data-type');
        const id = parseInt(this.getAttribute('data-id'));
        const formData = new FormData(this);

        const updatedItem = {
            code: formData.get('code'),
            name: formData.get('name'),
            weight: parseFloat(formData.get('weight')),
            quantity: parseInt(formData.get('quantity'))
        };

        if (type === 'mixture') {
            updatedItem.components = formData.get('components');
        }

        if (type === 'mill') {
            updatedItem.type = formData.get('type');
            updatedItem.capacity = parseInt(formData.get('capacity'));
            updatedItem.efficiency = parseInt(formData.get('efficiency'));
            updatedItem.workingHours = parseInt(formData.get('workingHours'));
            updatedItem.operatingCost = parseFloat(formData.get('operatingCost'));
            updatedItem.status = formData.get('status');
            updatedItem.lastMaintenance = formData.get('lastMaintenance');
            updatedItem.nextMaintenance = formData.get('nextMaintenance');
            updatedItem.maintenanceType = formData.get('maintenanceType');
            updatedItem.priority = formData.get('priority');

            // Recalculate daily production
            const workingHours = parseInt(formData.get('workingHours')) || 0;
            const capacity = parseInt(formData.get('capacity')) || 0;
            const efficiency = parseInt(formData.get('efficiency')) || 0;
            updatedItem.dailyProduction = Math.round((capacity * workingHours * efficiency) / 100);
        }

        // Update the item in database
        switch(type) {
            case 'raw-material':
                db.updateRawMaterial(id, updatedItem);
                rawMaterials = db.getRawMaterials();
                loadRawMaterials();
                break;
            case 'finished-product':
                db.updateFinishedProduct(id, updatedItem);
                finishedProducts = db.getFinishedProducts();
                loadFinishedProducts();
                break;
            case 'mixture':
                db.updateMixture(id, updatedItem);
                mixtures = db.getMixtures();
                loadMixtures();
                break;
            case 'mill':
                db.updateMill(id, updatedItem);
                mills = db.getMills();
                loadMills();
                break;
        }

        closeEditModal();
        alert('تم تعديل العنصر بنجاح!');
    });
}

// Search functionality
function searchTable(tableId, searchTerm) {
    const table = document.getElementById(tableId);
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    
    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.getElementsByTagName('td');
        let found = false;
        
        for (let j = 0; j < cells.length - 1; j++) { // Exclude actions column
            if (cells[j].textContent.toLowerCase().includes(searchTerm.toLowerCase())) {
                found = true;
                break;
            }
        }
        
        row.style.display = found ? '' : 'none';
    }
}

// Edit and Delete functions
function editItem(type, id) {
    let item = null;
    let title = '';

    // Find the item to edit
    switch(type) {
        case 'raw-material':
            item = rawMaterials.find(i => i.id === id);
            title = 'تعديل مادة خام';
            break;
        case 'finished-product':
            item = finishedProducts.find(i => i.id === id);
            title = 'تعديل مادة جاهزة';
            break;
        case 'mixture':
            item = mixtures.find(i => i.id === id);
            title = 'تعديل خلطة';
            break;
        case 'mill':
            item = mills.find(i => i.id === id);
            title = 'تعديل مطحنة';
            break;
    }

    if (!item) {
        alert('العنصر غير موجود!');
        return;
    }

    openEditModal(type, item, title);
}

function openEditModal(type, item, title) {
    document.getElementById('editModal').style.display = 'block';
    document.getElementById('editModalTitle').textContent = title;

    let fields = '';

    switch(type) {
        case 'raw-material':
            fields = `
                <div class="form-row">
                    <div class="form-group">
                        <label for="editCode">رقم الكود</label>
                        <input type="text" id="editCode" name="code" value="${item.code}" required>
                    </div>
                    <div class="form-group">
                        <label for="editName">اسم المادة</label>
                        <input type="text" id="editName" name="name" value="${item.name}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editWeight">الوزن (كجم)</label>
                        <input type="number" id="editWeight" name="weight" value="${item.weight}" required>
                    </div>
                    <div class="form-group">
                        <label for="editQuantity">الكمية</label>
                        <input type="number" id="editQuantity" name="quantity" value="${item.quantity}" required>
                    </div>
                </div>
            `;
            break;
        case 'finished-product':
            fields = `
                <div class="form-row">
                    <div class="form-group">
                        <label for="editCode">رقم الكود</label>
                        <input type="text" id="editCode" name="code" value="${item.code}" required>
                    </div>
                    <div class="form-group">
                        <label for="editName">اسم المنتج</label>
                        <input type="text" id="editName" name="name" value="${item.name}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editWeight">الوزن (كجم)</label>
                        <input type="number" id="editWeight" name="weight" value="${item.weight}" required>
                    </div>
                    <div class="form-group">
                        <label for="editQuantity">الكمية المتاحة</label>
                        <input type="number" id="editQuantity" name="quantity" value="${item.quantity}" required>
                    </div>
                </div>
            `;
            break;
        case 'mixture':
            fields = `
                <div class="form-row">
                    <div class="form-group">
                        <label for="editCode">كود التشغيل</label>
                        <input type="text" id="editCode" name="code" value="${item.code}" required>
                    </div>
                    <div class="form-group">
                        <label for="editName">اسم الخلطة</label>
                        <input type="text" id="editName" name="name" value="${item.name}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editComponents">المكونات</label>
                        <textarea id="editComponents" name="components" rows="3" required>${item.components}</textarea>
                    </div>
                    <div class="form-group">
                        <label for="editWeight">الوزن الإجمالي (كجم)</label>
                        <input type="number" id="editWeight" name="weight" value="${item.weight}" required>
                    </div>
                </div>
            `;
            break;
        case 'mill':
            fields = `
                <div class="form-row">
                    <div class="form-group">
                        <label for="editCode">رقم المطحنة</label>
                        <input type="text" id="editCode" name="code" value="${item.code}" required>
                    </div>
                    <div class="form-group">
                        <label for="editName">اسم المطحنة</label>
                        <input type="text" id="editName" name="name" value="${item.name}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editType">النوع</label>
                        <select id="editType" name="type" required>
                            <option value="مطحنة ثقيلة" ${item.type === 'مطحنة ثقيلة' ? 'selected' : ''}>مطحنة ثقيلة</option>
                            <option value="مطحنة متوسطة" ${item.type === 'مطحنة متوسطة' ? 'selected' : ''}>مطحنة متوسطة</option>
                            <option value="مطحنة خفيفة" ${item.type === 'مطحنة خفيفة' ? 'selected' : ''}>مطحنة خفيفة</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editCapacity">السعة (كجم/ساعة)</label>
                        <input type="number" id="editCapacity" name="capacity" value="${item.capacity}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editEfficiency">نسبة التشغيل (%)</label>
                        <input type="number" id="editEfficiency" name="efficiency" min="0" max="100" value="${item.efficiency}" required>
                    </div>
                    <div class="form-group">
                        <label for="editWorkingHours">ساعات التشغيل اليومية</label>
                        <input type="number" id="editWorkingHours" name="workingHours" min="0" max="24" value="${item.workingHours || 0}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editOperatingCost">تكلفة التشغيل (ريال/ساعة)</label>
                        <input type="number" id="editOperatingCost" name="operatingCost" min="0" value="${item.operatingCost || 0}" required>
                    </div>
                    <div class="form-group">
                        <label for="editStatus">حالة التشغيل</label>
                        <select id="editStatus" name="status" required>
                            <option value="نشطة" ${item.status === 'نشطة' ? 'selected' : ''}>نشطة</option>
                            <option value="متوقفة" ${item.status === 'متوقفة' ? 'selected' : ''}>متوقفة</option>
                            <option value="صيانة" ${item.status === 'صيانة' ? 'selected' : ''}>صيانة</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editLastMaintenance">آخر صيانة</label>
                        <input type="date" id="editLastMaintenance" name="lastMaintenance" value="${item.lastMaintenance}" required>
                    </div>
                    <div class="form-group">
                        <label for="editNextMaintenance">الصيانة القادمة</label>
                        <input type="date" id="editNextMaintenance" name="nextMaintenance" value="${item.nextMaintenance || ''}">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editMaintenanceType">نوع الصيانة</label>
                        <select id="editMaintenanceType" name="maintenanceType">
                            <option value="صيانة دورية" ${item.maintenanceType === 'صيانة دورية' ? 'selected' : ''}>صيانة دورية</option>
                            <option value="فحص شامل" ${item.maintenanceType === 'فحص شامل' ? 'selected' : ''}>فحص شامل</option>
                            <option value="تغيير قطع" ${item.maintenanceType === 'تغيير قطع' ? 'selected' : ''}>تغيير قطع</option>
                            <option value="إصلاح عطل" ${item.maintenanceType === 'إصلاح عطل' ? 'selected' : ''}>إصلاح عطل</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editPriority">الأولوية</label>
                        <select id="editPriority" name="priority">
                            <option value="عالية" ${item.priority === 'عالية' ? 'selected' : ''}>عالية</option>
                            <option value="متوسطة" ${item.priority === 'متوسطة' ? 'selected' : ''}>متوسطة</option>
                            <option value="منخفضة" ${item.priority === 'منخفضة' ? 'selected' : ''}>منخفضة</option>
                        </select>
                    </div>
                </div>
            `;
            break;
    }

    document.getElementById('editFormFields').innerHTML = fields;

    // Store the item data for form submission
    document.getElementById('editForm').setAttribute('data-type', type);
    document.getElementById('editForm').setAttribute('data-id', item.id);
}

function closeEditModal() {
    document.getElementById('editModal').style.display = 'none';
    document.getElementById('editForm').reset();
}

function deleteItem(type, id) {
    if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
        switch(type) {
            case 'raw-material':
                db.deleteRawMaterial(id);
                rawMaterials = db.getRawMaterials();
                loadRawMaterials();
                break;
            case 'finished-product':
                db.deleteFinishedProduct(id);
                finishedProducts = db.getFinishedProducts();
                loadFinishedProducts();
                break;
            case 'mixture':
                db.deleteMixture(id);
                mixtures = db.getMixtures();
                loadMixtures();
                break;
            case 'mill':
                db.deleteMill(id);
                mills = db.getMills();
                loadMills();
                break;
        }
        alert('تم حذف العنصر بنجاح!');
    }
}

// Export and Print functions with Arabic support
function exportToPDF(type) {
    // Create a temporary container for the report
    const reportContainer = createReportContainer(type);
    document.body.appendChild(reportContainer);

    // Use html2canvas to capture the content
    html2canvas(reportContainer, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: reportContainer.scrollWidth,
        height: reportContainer.scrollHeight
    }).then(canvas => {
        const { jsPDF } = window.jspdf;
        const pdf = new jsPDF('p', 'mm', 'a4');

        const imgWidth = 210; // A4 width in mm
        const pageHeight = 295; // A4 height in mm
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let heightLeft = imgHeight;

        let position = 0;

        // Add first page
        pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        // Add additional pages if needed
        while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
        }

        // Get title for filename
        const title = getReportTitle(type);
        pdf.save(`${title}.pdf`);

        // Remove temporary container
        document.body.removeChild(reportContainer);
    }).catch(error => {
        console.error('Error generating PDF:', error);
        document.body.removeChild(reportContainer);
        alert('حدث خطأ في إنشاء ملف PDF. سيتم استخدام الطريقة البديلة.');
        exportToPDFFallback(type);
    });
}

// Function to create user header for reports
function createUserHeader() {
    const userData = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const currentDate = formatArabicDate();
    const currentTime = new Date().toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });

    return `
        <div style="background: linear-gradient(135deg, #8b5a3c, #d4a574); color: white; padding: 20px; border-radius: 15px; margin-bottom: 30px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                <!-- Company Logo and Name -->
                <div style="display: flex; align-items: center;">
                    <div style="margin-left: 20px;">
                        <svg width="50" height="50" viewBox="0 0 80 80" style="filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));">
                            <g transform="translate(40, 40)">
                                <path d="M -8 2 Q -8 1 -7 1 L 7 1 Q 8 1 8 2 L 8 9 Q 8 11 6 11 L -6 11 Q -8 11 -8 9 Z" fill="#ff9a56"/>
                                <path d="M -6 -3 Q -6 -4 -5 -4 L 5 -4 Q 6 -4 6 -3 L 6 1 L -6 1 Z" fill="#fff"/>
                                <circle cx="9" cy="-5" r="2.5" fill="#ff6b9d"/>
                                <circle cx="0" cy="0" r="4" fill="#fff" opacity="0.3"/>
                                <circle cx="0" cy="0" r="2" fill="#fff"/>
                            </g>
                        </svg>
                    </div>
                    <div>
                        <h2 style="margin: 0; font-size: 22px; font-weight: bold;">مطحنة</h2>
                        <p style="margin: 3px 0 0 0; font-size: 13px; opacity: 0.9;">نظام إدارة المخزون</p>
                    </div>
                </div>

                <!-- Simplified User Information -->
                <div style="text-align: left;">
                    <div style="background: rgba(255,255,255,0.2); padding: 12px 20px; border-radius: 10px;">
                        <div style="display: flex; gap: 25px; font-size: 14px; align-items: center;">
                            <div><i class="fas fa-user" style="margin-left: 8px;"></i><strong>الاسم:</strong> ${userData.name || 'غير محدد'}</div>
                            <div><i class="fas fa-phone" style="margin-left: 8px;"></i><strong>الهاتف:</strong> ${userData.phone || 'غير محدد'}</div>
                        </div>
                    </div>
                </div>

                <!-- Date and Time -->
                <div style="text-align: center;">
                    <div style="background: rgba(255,255,255,0.15); padding: 8px 15px; border-radius: 8px;">
                        <div style="display: flex; gap: 20px; font-size: 13px; align-items: center;">
                            <div><i class="fas fa-calendar-alt" style="margin-left: 6px;"></i>${currentDate}</div>
                            <div><i class="fas fa-clock" style="margin-left: 6px;"></i>${currentTime}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function createReportContainer(type) {
    const container = document.createElement('div');
    container.style.cssText = `
        position: absolute;
        top: -10000px;
        left: -10000px;
        width: 800px;
        background: white;
        padding: 40px;
        font-family: 'Cairo', Arial, sans-serif;
        direction: rtl;
        color: #333;
        line-height: 1.6;
    `;

    let title = '';
    let content = '';

    switch(type) {
        case 'raw-materials':
            title = 'تقرير المواد الخام';
            content = generateRawMaterialsReport();
            break;
        case 'finished-products':
            title = 'تقرير المواد الجاهزة';
            content = generateFinishedProductsReport();
            break;
        case 'mixtures':
            title = 'تقرير الخلطات';
            content = generateMixturesReport();
            break;
        case 'mills':
            title = 'تقرير المطاحين الشامل';
            content = generateMillsReport();
            break;
    }

    container.innerHTML = `
        ${createUserHeader()}
        <div style="text-align: center; margin-bottom: 30px; border-bottom: 3px solid #8b5a3c; padding-bottom: 20px;">
            <h1 style="color: #2c3e50; font-size: 28px; margin: 0;">${title}</h1>
        </div>
        ${content}
        <div style="margin-top: 40px; text-align: center; color: #7f8c8d; font-size: 12px; border-top: 1px solid #ecf0f1; padding-top: 20px;">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة مخزون مطحنة</p>
        </div>
    `;

    return container;
}

function generateRawMaterialsReport() {
    const totalItems = rawMaterials.length;
    const totalWeight = rawMaterials.reduce((sum, item) => sum + (item.weight * item.quantity), 0);

    return `
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
            <h3 style="color: #2c3e50; margin-bottom: 15px;">ملخص المواد الخام</h3>
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;">
                <div style="text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #3498db;">${totalItems}</div>
                    <div style="color: #7f8c8d;">إجمالي الأصناف</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #27ae60;">${totalWeight.toFixed(2)}</div>
                    <div style="color: #7f8c8d;">إجمالي الوزن (كجم)</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #e74c3c;">${rawMaterials.reduce((sum, item) => sum + item.quantity, 0)}</div>
                    <div style="color: #7f8c8d;">إجمالي الكمية</div>
                </div>
            </div>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
            <thead>
                <tr style="background: #34495e; color: white;">
                    <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">رقم الكود</th>
                    <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">اسم المادة</th>
                    <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">الوزن (كجم)</th>
                    <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">الكمية</th>
                    <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">تاريخ الإضافة</th>
                </tr>
            </thead>
            <tbody>
                ${rawMaterials.map((item, index) => `
                    <tr style="background: ${index % 2 === 0 ? '#f8f9fa' : 'white'};">
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${item.code}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${item.name}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${item.weight}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${item.quantity}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${formatStoredDate(item.date)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
}

function generateFinishedProductsReport() {
    const totalItems = finishedProducts.length;
    const totalWeight = finishedProducts.reduce((sum, item) => sum + (item.weight * item.quantity), 0);

    return `
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
            <h3 style="color: #2c3e50; margin-bottom: 15px;">ملخص المواد الجاهزة</h3>
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;">
                <div style="text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #3498db;">${totalItems}</div>
                    <div style="color: #7f8c8d;">إجمالي المنتجات</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #27ae60;">${totalWeight.toFixed(2)}</div>
                    <div style="color: #7f8c8d;">إجمالي الوزن (كجم)</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #e74c3c;">${finishedProducts.reduce((sum, item) => sum + item.quantity, 0)}</div>
                    <div style="color: #7f8c8d;">إجمالي الكمية</div>
                </div>
            </div>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
            <thead>
                <tr style="background: #27ae60; color: white;">
                    <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">رقم الكود</th>
                    <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">اسم المنتج</th>
                    <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">الوزن (كجم)</th>
                    <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">الكمية المتاحة</th>
                    <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">تاريخ الإنتاج</th>
                </tr>
            </thead>
            <tbody>
                ${finishedProducts.map((item, index) => `
                    <tr style="background: ${index % 2 === 0 ? '#f8f9fa' : 'white'};">
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${item.code}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${item.name}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${item.weight}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${item.quantity}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${formatStoredDate(item.date)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
}

function generateMixturesReport() {
    return `
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
            <h3 style="color: #2c3e50; margin-bottom: 15px;">ملخص الخلطات</h3>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                <div style="text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #3498db;">${mixtures.length}</div>
                    <div style="color: #7f8c8d;">إجمالي الخلطات</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #27ae60;">${mixtures.reduce((sum, item) => sum + item.weight, 0)}</div>
                    <div style="color: #7f8c8d;">إجمالي الوزن (كجم)</div>
                </div>
            </div>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
            <thead>
                <tr style="background: #e74c3c; color: white;">
                    <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">كود التشغيل</th>
                    <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">اسم الخلطة</th>
                    <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">المكونات</th>
                    <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">الوزن الإجمالي</th>
                    <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">تاريخ الإنشاء</th>
                </tr>
            </thead>
            <tbody>
                ${mixtures.map((item, index) => `
                    <tr style="background: ${index % 2 === 0 ? '#f8f9fa' : 'white'};">
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${item.code}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${item.name}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${item.components}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${item.weight}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${formatStoredDate(item.date)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
}

function generateMillsReport() {
    const activeMills = mills.filter(m => m.status === 'نشطة').length;
    const totalProduction = mills.reduce((sum, m) => sum + (m.dailyProduction || 0), 0);
    const avgEfficiency = mills.length > 0 ?
        Math.round(mills.reduce((sum, m) => sum + m.efficiency, 0) / mills.length) : 0;

    return `
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
            <h3 style="color: #2c3e50; margin-bottom: 15px;">ملخص المطاحين</h3>
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px;">
                <div style="text-align: center;">
                    <div style="font-size: 20px; font-weight: bold; color: #3498db;">${mills.length}</div>
                    <div style="color: #7f8c8d; font-size: 12px;">إجمالي المطاحين</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 20px; font-weight: bold; color: #27ae60;">${activeMills}</div>
                    <div style="color: #7f8c8d; font-size: 12px;">مطاحين نشطة</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 20px; font-weight: bold; color: #f39c12;">${totalProduction}</div>
                    <div style="color: #7f8c8d; font-size: 12px;">الإنتاج اليومي (كجم)</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 20px; font-weight: bold; color: #9b59b6;">${avgEfficiency}%</div>
                    <div style="color: #7f8c8d; font-size: 12px;">متوسط الكفاءة</div>
                </div>
            </div>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px; font-size: 12px;">
            <thead>
                <tr style="background: #9b59b6; color: white;">
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">رقم المطحنة</th>
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">اسم المطحنة</th>
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">النوع</th>
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">السعة</th>
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">الكفاءة</th>
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">ساعات التشغيل</th>
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">الإنتاج اليومي</th>
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">التكلفة/ساعة</th>
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">الحالة</th>
                </tr>
            </thead>
            <tbody>
                ${mills.map((item, index) => `
                    <tr style="background: ${index % 2 === 0 ? '#f8f9fa' : 'white'};">
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${item.code}</td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${item.name}</td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${item.type}</td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${item.capacity} كجم/ساعة</td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${item.efficiency}%</td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${item.workingHours || 0} ساعة</td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${item.dailyProduction || 0} كجم</td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${item.operatingCost || 0} ريال</td>
                        <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">${item.status}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
}

function getReportTitle(type) {
    switch(type) {
        case 'raw-materials': return 'تقرير_المواد_الخام';
        case 'finished-products': return 'تقرير_المواد_الجاهزة';
        case 'mixtures': return 'تقرير_الخلطات';
        case 'mills': return 'تقرير_المطاحين';
        default: return 'تقرير';
    }
}

function exportToPDFFallback(type) {
    // Fallback method using basic jsPDF for simple text
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    const title = getReportTitle(type).replace(/_/g, ' ');

    // Add title in English as fallback
    doc.setFontSize(16);
    doc.text(`Report: ${type}`, 105, 20, { align: 'center' });
    doc.text(`Date: ${formatArabicDate()}`, 105, 30, { align: 'center' });

    let yPosition = 50;
    doc.setFontSize(12);

    // Add data based on type
    let data = [];
    switch(type) {
        case 'raw-materials':
            data = rawMaterials;
            break;
        case 'finished-products':
            data = finishedProducts;
            break;
        case 'mixtures':
            data = mixtures;
            break;
        case 'mills':
            data = mills;
            break;
    }

    data.forEach((item, index) => {
        if (yPosition > 270) {
            doc.addPage();
            yPosition = 20;
        }

        doc.text(`${index + 1}. Code: ${item.code || item.id}`, 20, yPosition);
        yPosition += 10;
        doc.text(`   Name: ${item.name}`, 20, yPosition);
        yPosition += 15;
    });

    doc.save(`${title}.pdf`);
}

function printTable(type) {
    // Create a print-friendly version using the same report generation
    const reportContainer = createReportContainer(type);

    // Get report title in Arabic
    const titles = {
        'raw-materials': 'تقرير المواد الخام',
        'finished-products': 'تقرير المواد الجاهزة',
        'mixtures': 'تقرير الخلطات',
        'mills': 'تقرير المطاحين الشامل'
    };

    const title = titles[type] || 'تقرير';

    // Create print window
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>${title}</title>
            <meta charset="UTF-8">
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>
                body {
                    font-family: 'Cairo', Arial, sans-serif;
                    direction: rtl;
                    margin: 0;
                    padding: 20px;
                    line-height: 1.6;
                    color: #333;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 3px solid #3498db;
                    padding-bottom: 20px;
                }
                h1 {
                    color: #2c3e50;
                    font-size: 28px;
                    margin: 0 0 10px 0;
                }
                .date {
                    color: #7f8c8d;
                    margin: 0;
                }
                .summary-section {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                    margin-bottom: 30px;
                    page-break-inside: avoid;
                }
                .summary-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                    gap: 15px;
                    text-align: center;
                }
                .summary-item {
                    background: white;
                    padding: 15px;
                    border-radius: 8px;
                    border-right: 4px solid #3498db;
                }
                .summary-number {
                    font-size: 24px;
                    font-weight: bold;
                    color: #3498db;
                }
                .summary-label {
                    color: #7f8c8d;
                    font-size: 12px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                    page-break-inside: auto;
                    background: white;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 10px;
                    text-align: right;
                    page-break-inside: avoid;
                    font-size: 12px;
                }
                th {
                    background: #34495e;
                    color: white;
                    font-weight: bold;
                    page-break-after: avoid;
                }
                tbody tr:nth-child(even) {
                    background: #f8f9fa;
                }
                tbody tr:hover {
                    background: #e3f2fd;
                }
                h3 {
                    color: #2c3e50;
                    margin-bottom: 15px;
                    page-break-after: avoid;
                }
                .action-btn {
                    display: none !important;
                }
                .footer {
                    margin-top: 40px;
                    text-align: center;
                    color: #7f8c8d;
                    font-size: 12px;
                    border-top: 1px solid #ecf0f1;
                    padding-top: 20px;
                }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none !important; }
                    @page {
                        margin: 1cm;
                        size: A4;
                    }
                    .action-btn {
                        display: none !important;
                    }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>${title}</h1>
                <p class="date">تاريخ التقرير: ${formatArabicDate()}</p>
            </div>

            ${reportContainer.innerHTML}

            <div class="footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة مخزون مطحنة</p>
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();

    // Wait for content to load then print
    setTimeout(() => {
        printWindow.print();
    }, 1000);
}

// Reports functionality
function generateReport(reportType) {
    switch(reportType) {
        case 'raw-materials':
            exportToPDF('raw-materials');
            break;
        case 'finished-products':
            exportToPDF('finished-products');
            break;
        case 'mixtures':
            exportToPDF('mixtures');
            break;
        case 'inventory-summary':
            generateInventorySummary();
            break;
        default:
            alert(`إنشاء تقرير ${reportType} - هذه الوظيفة قيد التطوير`);
    }
}

function generateInventorySummary() {
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    doc.setFont('Arial', 'normal');
    doc.setFontSize(18);
    doc.text('ملخص المخزون الشامل', 105, 20, { align: 'center' });

    let yPosition = 40;

    // Raw Materials Summary
    doc.setFontSize(14);
    doc.text('المواد الخام:', 20, yPosition);
    yPosition += 10;

    const rawMaterialsData = rawMaterials.map(item => [item.name, item.quantity, item.weight + ' كجم']);
    doc.autoTable({
        head: [['اسم المادة', 'الكمية', 'الوزن']],
        body: rawMaterialsData,
        startY: yPosition,
        styles: { fontSize: 10, font: 'Arial' },
        headStyles: { fillColor: [52, 73, 94] }
    });

    yPosition = doc.lastAutoTable.finalY + 20;

    // Finished Products Summary
    doc.text('المواد الجاهزة:', 20, yPosition);
    yPosition += 10;

    const finishedProductsData = finishedProducts.map(item => [item.name, item.quantity, item.weight + ' كجم']);
    doc.autoTable({
        head: [['اسم المنتج', 'الكمية', 'الوزن']],
        body: finishedProductsData,
        startY: yPosition,
        styles: { fontSize: 10, font: 'Arial' },
        headStyles: { fillColor: [39, 174, 96] }
    });

    yPosition = doc.lastAutoTable.finalY + 20;

    // Mixtures Summary
    doc.text('الخلطات:', 20, yPosition);
    yPosition += 10;

    const mixturesData = mixtures.map(item => [item.name, item.weight + ' كجم', item.components]);
    doc.autoTable({
        head: [['اسم الخلطة', 'الوزن', 'المكونات']],
        body: mixturesData,
        startY: yPosition,
        styles: { fontSize: 10, font: 'Arial' },
        headStyles: { fillColor: [231, 76, 60] }
    });

    doc.save('ملخص_المخزون_الشامل.pdf');
}

// Fullscreen functionality
let isFullscreen = false;
let currentPage = null;

function toggleFullscreen() {
    const body = document.body;
    const currentPageElement = document.querySelector('.page.active');
    const fullscreenToggle = document.querySelector('.fullscreen-toggle');

    if (!isFullscreen) {
        // Enter fullscreen mode
        isFullscreen = true;
        currentPage = currentPageElement;

        // Add fullscreen class to body
        body.classList.add('fullscreen-mode');

        // Update button
        fullscreenToggle.classList.add('active');
        fullscreenToggle.innerHTML = '<i class="fas fa-compress"></i> خروج من ملء الشاشة';

        // Add exit button
        const exitButton = document.createElement('button');
        exitButton.className = 'fullscreen-exit';
        exitButton.innerHTML = '<i class="fas fa-times"></i>';
        exitButton.onclick = toggleFullscreen;
        exitButton.title = 'خروج من ملء الشاشة';
        body.appendChild(exitButton);

        // Hide other pages
        document.querySelectorAll('.page').forEach(page => {
            if (page !== currentPageElement) {
                page.style.display = 'none';
            }
        });

        // Adjust current page
        if (currentPageElement) {
            currentPageElement.style.display = 'block';
            currentPageElement.style.position = 'relative';
            currentPageElement.style.zIndex = '10000';
        }

    } else {
        // Exit fullscreen mode
        exitFullscreen();
    }
}

function exitFullscreen() {
    const body = document.body;
    const fullscreenToggle = document.querySelector('.fullscreen-toggle');
    const exitButton = document.querySelector('.fullscreen-exit');

    isFullscreen = false;

    // Remove fullscreen class
    body.classList.remove('fullscreen-mode');

    // Update button
    if (fullscreenToggle) {
        fullscreenToggle.classList.remove('active');
        fullscreenToggle.innerHTML = '<i class="fas fa-expand"></i> ملء الشاشة';
    }

    // Remove exit button
    if (exitButton) {
        exitButton.remove();
    }

    // Show all pages
    document.querySelectorAll('.page').forEach(page => {
        page.style.display = '';
        page.style.position = '';
        page.style.zIndex = '';
    });

    // Reset current page
    if (currentPage) {
        currentPage = null;
    }
}

// Handle ESC key to exit fullscreen
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && isFullscreen) {
        exitFullscreen();
    }
});

// Update page switching to handle fullscreen
function showPage(pageId) {
    // Hide all pages
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
        if (!isFullscreen) {
            page.style.display = 'none';
        }
    });

    // Show selected page
    const selectedPage = document.getElementById(pageId);
    if (selectedPage) {
        selectedPage.classList.add('active');
        selectedPage.style.display = 'block';

        // Update current page reference for fullscreen
        if (isFullscreen) {
            currentPage = selectedPage;
            // Hide other pages in fullscreen
            document.querySelectorAll('.page').forEach(page => {
                if (page !== selectedPage) {
                    page.style.display = 'none';
                }
            });
        }
    }

    // Update navigation
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });

    const activeNavItem = document.querySelector(`[data-page="${pageId}"]`);
    if (activeNavItem) {
        activeNavItem.classList.add('active');
    }
}

// Close modal when clicking outside
window.onclick = function(event) {
    const addModal = document.getElementById('addModal');
    const editModal = document.getElementById('editModal');
    const recipeModal = document.getElementById('recipeModal');
    const productionModal = document.getElementById('productionModal');
    const productionDetailsModal = document.getElementById('productionDetailsModal');

    if (event.target === addModal) {
        closeModal();
    } else if (event.target === editModal) {
        closeEditModal();
    } else if (event.target === recipeModal) {
        closeRecipeModal();
    } else if (event.target === productionModal) {
        closeProductionModal();
    } else if (event.target === productionDetailsModal) {
        closeProductionDetailsModal();
    }
}

// Add event listeners for production form
document.addEventListener('DOMContentLoaded', function() {
    // Add event listener for quantity change to update calculations
    const quantityInput = document.getElementById('productionQuantity');
    if (quantityInput) {
        quantityInput.addEventListener('input', updateProductionCalculations);
    }

    // Add event listener for priority change to update summary
    const prioritySelect = document.getElementById('priority');
    if (prioritySelect) {
        prioritySelect.addEventListener('change', function() {
            const priority = this.value;
            document.getElementById('summaryPriority').textContent = priority || '-';
        });
    }

    // Add event listener for total weight change to update ingredient weights
    const totalWeightInput = document.getElementById('totalWeight');
    if (totalWeightInput) {
        totalWeightInput.addEventListener('input', updateTotalPercentage);
    }
});
